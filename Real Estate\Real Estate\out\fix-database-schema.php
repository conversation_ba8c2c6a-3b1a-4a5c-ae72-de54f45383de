<?php
// Database Schema Fix Script
// This script fixes database schema issues and missing columns

echo "<h1>🔧 Database Schema Fix</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

try {
    // Create database file if it doesn't exist
    if (!file_exists('prisma/dev.db')) {
        echo "<p class='info'>ℹ️ Database file not found. Creating new database...</p>";
        
        if (!is_dir('prisma')) {
            mkdir('prisma', 0755, true);
            echo "<p class='success'>✅ Created prisma directory</p>";
        }
        
        touch('prisma/dev.db');
        chmod('prisma/dev.db', 0666);
        echo "<p class='success'>✅ Created database file</p>";
    }
    
    // Connect to database
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Drop existing tables to recreate with correct schema
    echo "<p class='info'>ℹ️ Recreating database tables with correct schema...</p>";
    
    $db->exec("DROP TABLE IF EXISTS Property");
    $db->exec("DROP TABLE IF EXISTS User");
    echo "<p class='success'>✅ Dropped existing tables</p>";
    
    // Create User table with correct schema
    $db->exec("
        CREATE TABLE User (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            phone TEXT,
            role TEXT NOT NULL DEFAULT 'USER',
            isActive INTEGER NOT NULL DEFAULT 1,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL
        )
    ");
    echo "<p class='success'>✅ Created User table</p>";
    
    // Create Property table with ALL required columns
    $db->exec("
        CREATE TABLE Property (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            price INTEGER NOT NULL,
            currency TEXT NOT NULL DEFAULT 'INR',
            type TEXT NOT NULL,
            listingType TEXT NOT NULL DEFAULT 'SALE',
            accommodationType TEXT,
            pgRoomType TEXT,
            pgGenderPreference TEXT,
            status TEXT NOT NULL DEFAULT 'AVAILABLE',
            bedrooms INTEGER,
            bathrooms INTEGER,
            area INTEGER,
            address TEXT NOT NULL,
            city TEXT NOT NULL,
            state TEXT NOT NULL,
            pincode TEXT NOT NULL,
            latitude REAL,
            longitude REAL,
            images TEXT,
            amenities TEXT,
            is_featured INTEGER NOT NULL DEFAULT 0,
            is_approved INTEGER NOT NULL DEFAULT 0,
            approval_status TEXT NOT NULL DEFAULT 'APPROVED',
            view_count INTEGER NOT NULL DEFAULT 0,
            isActive INTEGER NOT NULL DEFAULT 1,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            approvedAt TEXT,
            approvedBy TEXT,
            ownerId TEXT NOT NULL,
            FOREIGN KEY (ownerId) REFERENCES User(id)
        )
    ");
    echo "<p class='success'>✅ Created Property table with all required columns</p>";
    
    // Create admin user
    echo "<p class='info'>ℹ️ Creating admin user...</p>";
    
    $hashedPassword = password_hash('Admin@2024!', PASSWORD_DEFAULT);
    $adminId = 'admin_' . uniqid();
    $now = date('Y-m-d H:i:s');
    
    $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
    $stmt->execute([
        $adminId,
        'Admin User',
        '<EMAIL>',
        $hashedPassword,
        'ADMIN',
        1,
        $now,
        $now
    ]);
    
    echo "<p class='success'>✅ Admin user created</p>";
    echo "<p><strong>Email:</strong> <EMAIL></p>";
    echo "<p><strong>Password:</strong> Admin@2024!</p>";
    
    // Create sample properties
    echo "<p class='info'>ℹ️ Creating sample properties...</p>";
    
    $sampleProperties = [
        [
            'title' => 'Luxury 3BHK Apartment in Bandra',
            'description' => 'Beautiful 3BHK apartment with modern amenities, sea view, and prime location in Bandra West. Features include spacious rooms, modular kitchen, and 24/7 security.',
            'price' => 8500000,
            'type' => 'APARTMENT',
            'listingType' => 'SALE',
            'bedrooms' => 3,
            'bathrooms' => 2,
            'area' => 1200,
            'address' => '123 Sea View Apartments, Bandra West',
            'city' => 'Mumbai',
            'state' => 'Maharashtra',
            'pincode' => '400050',
            'amenities' => '["Swimming Pool", "Gym", "Parking", "Security", "Elevator", "Garden"]',
            'images' => '["https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop", "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop"]'
        ],
        [
            'title' => 'Affordable 1BHK Flat for Rent',
            'description' => 'Cozy 1BHK flat available for rent in a peaceful residential area with all basic amenities. Perfect for young professionals and couples.',
            'price' => 25000,
            'type' => 'APARTMENT',
            'listingType' => 'RENT',
            'bedrooms' => 1,
            'bathrooms' => 1,
            'area' => 600,
            'address' => '456 Green Valley Society, Andheri East',
            'city' => 'Mumbai',
            'state' => 'Maharashtra',
            'pincode' => '400069',
            'amenities' => '["Parking", "Security", "Water Supply", "Power Backup"]',
            'images' => '["https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop", "https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop"]'
        ],
        [
            'title' => 'Premium PG for Working Professionals',
            'description' => 'Fully furnished PG accommodation with AC, WiFi, meals, and all modern facilities. Located in prime area with easy access to IT parks.',
            'price' => 15000,
            'type' => 'PG',
            'listingType' => 'RENT',
            'pgRoomType' => 'SINGLE',
            'pgGenderPreference' => 'MALE',
            'area' => 150,
            'address' => '789 Executive PG, Powai',
            'city' => 'Mumbai',
            'state' => 'Maharashtra',
            'pincode' => '400076',
            'amenities' => '["WiFi", "AC", "Meals", "Laundry", "Housekeeping", "Security"]',
            'images' => '["https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop", "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop"]'
        ],
        [
            'title' => 'Spacious 4BHK Villa with Garden',
            'description' => 'Independent villa with private garden, parking for 2 cars, and premium location. Perfect for families looking for luxury living.',
            'price' => 15000000,
            'type' => 'VILLA',
            'listingType' => 'SALE',
            'bedrooms' => 4,
            'bathrooms' => 3,
            'area' => 2500,
            'address' => '321 Garden Villas, Juhu',
            'city' => 'Mumbai',
            'state' => 'Maharashtra',
            'pincode' => '400049',
            'amenities' => '["Garden", "Parking", "Security", "Swimming Pool", "Servant Room"]',
            'images' => '["https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop", "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop"]'
        ]
    ];
    
    foreach ($sampleProperties as $property) {
        $propertyId = 'prop_' . uniqid();
        
        $stmt = $db->prepare('
            INSERT INTO Property (
                id, title, description, price, type, listingType, bedrooms, bathrooms, area,
                address, city, state, pincode, amenities, images, approval_status,
                isActive, createdAt, updatedAt, approvedAt, ownerId
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ');
        
        $stmt->execute([
            $propertyId,
            $property['title'],
            $property['description'],
            $property['price'],
            $property['type'],
            $property['listingType'],
            $property['bedrooms'] ?? null,
            $property['bathrooms'] ?? null,
            $property['area'],
            $property['address'],
            $property['city'],
            $property['state'],
            $property['pincode'],
            $property['amenities'],
            $property['images'],
            'APPROVED', // Pre-approve all sample properties
            1,
            $now,
            $now,
            $now,
            $adminId
        ]);
    }
    
    echo "<p class='success'>✅ Created " . count($sampleProperties) . " sample properties</p>";
    
    // Verify everything
    $stmt = $db->query('SELECT COUNT(*) as count FROM Property WHERE approval_status = "APPROVED"');
    $approvedCount = $stmt->fetch()['count'];
    
    $stmt = $db->query('SELECT COUNT(*) as count FROM User WHERE role = "ADMIN"');
    $adminCount = $stmt->fetch()['count'];
    
    echo "<h2>✅ Database Fixed Successfully!</h2>";
    echo "<p class='success'>Database schema has been corrected and sample data added.</p>";
    echo "<p><strong>Statistics:</strong></p>";
    echo "<ul>";
    echo "<li>Admin users: $adminCount</li>";
    echo "<li>Approved properties: $approvedCount</li>";
    echo "</ul>";
    
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='properties/' target='_blank'>Visit Properties Page</a> - Should show $approvedCount properties</li>";
    echo "<li><a href='admin/login/' target='_blank'>Admin Login</a> - Use <EMAIL> / Admin@2024!</li>";
    echo "<li><a href='verify-deployment.php' target='_blank'>Run Full Verification</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check file permissions and try again.</p>";
}

echo "<hr>";
echo "<p><small>Generated on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
