<?php
// Complete Deployment Verification Script
// Run this after uploading to verify everything is working

echo "<h1>🔍 Complete Deployment Verification</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f8f9fa;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;font-weight:bold;} 
.error{color:#dc3545;font-weight:bold;} 
.warning{color:#ffc107;font-weight:bold;} 
.info{color:#17a2b8;font-weight:bold;}
.test-section{background:#f8f9fa;padding:15px;margin:10px 0;border-radius:8px;border-left:4px solid #007bff;}
.status-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:15px;margin:20px 0;}
.status-card{background:white;padding:15px;border-radius:8px;border:1px solid #dee2e6;}
pre{background:#f8f9fa;padding:10px;border-radius:5px;overflow-x:auto;}
.btn{display:inline-block;padding:10px 20px;background:#007bff;color:white;text-decoration:none;border-radius:5px;margin:5px;}
.btn:hover{background:#0056b3;}
</style>";

echo "<div class='container'>";

$allTestsPassed = true;
$testResults = [];

// Test 1: File Structure
echo "<div class='test-section'>";
echo "<h2>📁 File Structure Check</h2>";

$requiredFiles = [
    'php-backend/api/properties/search.php' => 'Properties Search API',
    'php-backend/api/auth/login.php' => 'Admin Login API',
    'php-backend/config/database.php' => 'Database Configuration',
    'prisma/schema.prisma' => 'Database Schema',
    'index.html' => 'Homepage',
    'properties/index.html' => 'Properties Page'
];

$requiredDirs = [
    'php-backend/uploads' => 'Backend Uploads Directory',
    'uploads' => 'Frontend Uploads Directory',
    'prisma' => 'Database Directory'
];

$filesPassed = true;
foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $description: $file</p>";
    } else {
        echo "<p class='error'>❌ $description: $file (MISSING)</p>";
        $filesPassed = false;
    }
}

foreach ($requiredDirs as $dir => $description) {
    if (is_dir($dir)) {
        echo "<p class='success'>✅ $description: $dir</p>";
    } else {
        echo "<p class='error'>❌ $description: $dir (MISSING)</p>";
        $filesPassed = false;
    }
}

$testResults['files'] = $filesPassed;
if (!$filesPassed) $allTestsPassed = false;
echo "</div>";

// Test 2: Database Connection
echo "<div class='test-section'>";
echo "<h2>🗄️ Database Connection</h2>";

$dbPassed = true;
try {
    if (file_exists('prisma/dev.db')) {
        $db = new PDO('sqlite:prisma/dev.db');
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Database file exists and connection successful</p>";
        
        // Check tables
        $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
        $tableNames = array_column($tables, 'name');
        
        if (in_array('User', $tableNames) && in_array('Property', $tableNames)) {
            echo "<p class='success'>✅ Required tables exist (User, Property)</p>";
        } else {
            echo "<p class='error'>❌ Required tables missing</p>";
            $dbPassed = false;
        }
        
        // Check admin user
        $stmt = $db->query('SELECT COUNT(*) as count FROM User WHERE role = "ADMIN"');
        $adminCount = $stmt->fetch()['count'];
        if ($adminCount > 0) {
            echo "<p class='success'>✅ Admin user exists ($adminCount found)</p>";
        } else {
            echo "<p class='error'>❌ No admin users found</p>";
            $dbPassed = false;
        }
        
        // Check properties
        $stmt = $db->query('SELECT COUNT(*) as count FROM Property WHERE approval_status = "APPROVED"');
        $propertyCount = $stmt->fetch()['count'];
        if ($propertyCount > 0) {
            echo "<p class='success'>✅ Approved properties exist ($propertyCount found)</p>";
        } else {
            echo "<p class='warning'>⚠️ No approved properties found</p>";
            echo "<p><a href='initialize-database.php' class='btn'>Initialize Sample Data</a></p>";
        }
        
    } else {
        echo "<p class='error'>❌ Database file not found</p>";
        echo "<p><a href='initialize-database.php' class='btn'>Create Database</a></p>";
        $dbPassed = false;
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    $dbPassed = false;
}

$testResults['database'] = $dbPassed;
if (!$dbPassed) $allTestsPassed = false;
echo "</div>";

// Test 3: API Endpoints
echo "<div class='test-section'>";
echo "<h2">🔌 API Endpoints Test</h2>";

$apiPassed = true;
$baseUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);

// Test search API
$searchUrl = "$baseUrl/php-backend/api/properties/search.php?limit=1";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($searchUrl, false, $context);
if ($response !== false) {
    $data = json_decode($response, true);
    if ($data && isset($data['properties'])) {
        echo "<p class='success'>✅ Properties Search API working</p>";
        echo "<p class='info'>Found " . count($data['properties']) . " properties</p>";
    } else {
        echo "<p class='error'>❌ Properties Search API returns invalid data</p>";
        $apiPassed = false;
    }
} else {
    echo "<p class='error'>❌ Properties Search API not accessible</p>";
    $apiPassed = false;
}

$testResults['api'] = $apiPassed;
if (!$apiPassed) $allTestsPassed = false;
echo "</div>";

// Test 4: Image Processing
echo "<div class='test-section'>";
echo "<h2>🖼️ Image Processing Test</h2>";

$imagesPassed = true;
$uploadDirs = ['php-backend/uploads', 'uploads'];
$totalImages = 0;

foreach ($uploadDirs as $dir) {
    if (is_dir($dir)) {
        $files = scandir($dir);
        $imageFiles = array_filter($files, function($file) {
            return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        });
        $count = count($imageFiles);
        $totalImages += $count;
        echo "<p class='success'>✅ $dir: $count image files</p>";
    } else {
        echo "<p class='error'>❌ $dir: Directory not found</p>";
        $imagesPassed = false;
    }
}

if ($totalImages > 0) {
    echo "<p class='success'>✅ Total images available: $totalImages</p>";
} else {
    echo "<p class='warning'>⚠️ No image files found</p>";
}

$testResults['images'] = $imagesPassed;
echo "</div>";

// Test 5: Frontend Pages
echo "<div class='test-section'>";
echo "<h2>🌐 Frontend Pages Test</h2>";

$frontendPages = [
    'index.html' => 'Homepage',
    'properties/index.html' => 'Properties Page',
    'admin/login/index.html' => 'Admin Login',
    'buy/index.html' => 'Buy Page',
    'rent/index.html' => 'Rent Page',
    'pg/index.html' => 'PG Page'
];

$frontendPassed = true;
foreach ($frontendPages as $page => $name) {
    if (file_exists($page)) {
        $url = "$baseUrl/$page";
        echo "<p class='success'>✅ $name: <a href='$url' target='_blank'>$url</a></p>";
    } else {
        echo "<p class='error'>❌ $name: $page (MISSING)</p>";
        $frontendPassed = false;
    }
}

$testResults['frontend'] = $frontendPassed;
if (!$frontendPassed) $allTestsPassed = false;
echo "</div>";

// Summary
echo "<div class='test-section'>";
echo "<h2>📊 Test Summary</h2>";

echo "<div class='status-grid'>";
foreach ($testResults as $test => $passed) {
    $status = $passed ? 'success' : 'error';
    $icon = $passed ? '✅' : '❌';
    $text = $passed ? 'PASSED' : 'FAILED';
    echo "<div class='status-card'>";
    echo "<h3>$icon " . ucfirst($test) . "</h3>";
    echo "<p class='$status'>$text</p>";
    echo "</div>";
}
echo "</div>";

if ($allTestsPassed) {
    echo "<div style='background:#d4edda;color:#155724;padding:20px;border-radius:8px;margin:20px 0;'>";
    echo "<h3>🎉 All Tests Passed!</h3>";
    echo "<p>Your Real Estate website is fully deployed and working correctly.</p>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='properties/' target='_blank'>Visit Properties Page</a></li>";
    echo "<li><a href='admin/login/' target='_blank'>Access Admin Panel</a> (<EMAIL> / Admin@2024!)</li>";
    echo "<li>Start adding your own properties through the admin panel</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background:#f8d7da;color:#721c24;padding:20px;border-radius:8px;margin:20px 0;'>";
    echo "<h3>⚠️ Some Tests Failed</h3>";
    echo "<p>Please fix the failed tests above before proceeding.</p>";
    echo "<p><strong>Quick Fixes:</strong></p>";
    echo "<ul>";
    echo "<li><a href='initialize-database.php' class='btn'>Initialize Database</a></li>";
    echo "<li><a href='test-properties-api.php' class='btn'>Test API</a></li>";
    echo "<li><a href='inspect-database.php' class='btn'>Inspect Database</a></li>";
    echo "</ul>";
    echo "</div>";
}
echo "</div>";

// Additional Tools
echo "<div class='test-section'>";
echo "<h2>🛠️ Additional Tools</h2>";
echo "<p>Use these tools for further debugging and management:</p>";
echo "<div>";
echo "<a href='test-properties-api.php' class='btn'>API Test</a>";
echo "<a href='inspect-database.php' class='btn'>Database Inspector</a>";
echo "<a href='production-diagnostics.php' class='btn'>System Diagnostics</a>";
echo "<a href='initialize-database.php' class='btn'>Initialize Database</a>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "<hr>";
echo "<p><small>Verification completed on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
