<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: admin-login-direct.php');
    exit;
}

$admin_user = $_SESSION['admin_user'];

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin-login-direct.php');
    exit;
}

// Get database stats with error handling
$totalProperties = 0;
$approvedProperties = 0;
$pendingProperties = 0;
$totalUsers = 0;
$recentProperties = [];
$dbError = null;

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if tables exist first
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('Property', $tables)) {
        // Get basic property count
        try {
            $result = $db->query('SELECT COUNT(*) FROM Property')->fetch();
            $totalProperties = $result[0];
        } catch (Exception $e) {
            // Fallback: count rows manually
            $result = $db->query('SELECT * FROM Property')->fetchAll();
            $totalProperties = count($result);
        }
        
        // Try to get approval status counts
        try {
            $columns = $db->query("PRAGMA table_info(Property)")->fetchAll(PDO::FETCH_ASSOC);
            $hasApprovalStatus = false;
            foreach ($columns as $col) {
                if ($col['name'] === 'approval_status') {
                    $hasApprovalStatus = true;
                    break;
                }
            }
            
            if ($hasApprovalStatus) {
                $result = $db->query('SELECT * FROM Property WHERE approval_status = "APPROVED"')->fetchAll();
                $approvedProperties = count($result);
                
                $result = $db->query('SELECT * FROM Property WHERE approval_status = "PENDING"')->fetchAll();
                $pendingProperties = count($result);
            } else {
                $approvedProperties = $totalProperties; // Assume all approved if no status column
            }
        } catch (Exception $e) {
            $approvedProperties = $totalProperties;
        }
        
        // Get recent properties
        try {
            $stmt = $db->query('SELECT * FROM Property ORDER BY createdAt DESC LIMIT 5');
            $recentProperties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // Fallback without ORDER BY
            $stmt = $db->query('SELECT * FROM Property LIMIT 5');
            $recentProperties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
    
    // Get user count
    if (in_array('User', $tables)) {
        try {
            $result = $db->query('SELECT COUNT(*) FROM User')->fetch();
            $totalUsers = $result[0];
        } catch (Exception $e) {
            $result = $db->query('SELECT * FROM User')->fetchAll();
            $totalUsers = count($result);
        }
    }
    
} catch (Exception $e) {
    $dbError = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Real Estate</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header h1 {
            font-size: 1.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .recent-properties {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .recent-properties h2 {
            padding: 1.5rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            margin: 0;
        }
        
        .property-list {
            padding: 1rem;
        }
        
        .property-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .property-item:last-child {
            border-bottom: none;
        }
        
        .property-info h4 {
            margin-bottom: 0.25rem;
            color: #333;
        }
        
        .property-info p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .property-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .action-btn {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-decoration: none;
            color: #333;
            text-align: center;
            transition: transform 0.2s;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        .action-btn .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🏠 Admin Dashboard</h1>
            <div class="user-info">
                <span>Welcome, <?= htmlspecialchars($admin_user['name']) ?></span>
                <a href="?logout=1" class="logout-btn">Logout</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="success-message">
            🎉 <strong>Admin Login Successful!</strong> You are now logged in and can access all admin functions.
        </div>
        
        <?php if (isset($dbError)): ?>
            <div class="error-message">
                ❌ Database Error: <?= htmlspecialchars($dbError) ?>
            </div>
        <?php else: ?>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Total Properties</h3>
                    <div class="number"><?= $totalProperties ?></div>
                </div>
                
                <div class="stat-card">
                    <h3>Approved Properties</h3>
                    <div class="number"><?= $approvedProperties ?></div>
                </div>
                
                <div class="stat-card">
                    <h3>Pending Approval</h3>
                    <div class="number"><?= $pendingProperties ?></div>
                </div>
                
                <div class="stat-card">
                    <h3>Total Users</h3>
                    <div class="number"><?= $totalUsers ?></div>
                </div>
            </div>
            
        <?php endif; ?>
        
        <div class="quick-actions">
            <a href="properties/" class="action-btn">
                <div class="icon">🏠</div>
                <div>View Properties</div>
            </a>
            
            <a href="properties/create/" class="action-btn">
                <div class="icon">➕</div>
                <div>Add Property</div>
            </a>
            
            <a href="database-diagnostic.php" class="action-btn">
                <div class="icon">🔍</div>
                <div>Database Diagnostic</div>
            </a>
            
            <a href="simple-test.php" class="action-btn">
                <div class="icon">🔧</div>
                <div>System Test</div>
            </a>
        </div>
    </div>
</body>
</html>
