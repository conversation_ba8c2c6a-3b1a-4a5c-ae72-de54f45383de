// API configuration for hybrid deployment
// This file configures the frontend to use PHP backend APIs

export const API_BASE_URL = '/php-backend/api'; // Use relative URL with proxy in development

console.log('🔍 API_BASE_URL:', API_BASE_URL);
console.log('🔍 NODE_ENV:', process.env.NODE_ENV);

export const API_ENDPOINTS = {
  // Authentication
  LOGIN: `${API_BASE_URL}/auth/login.php`,
  SIGNUP: `${API_BASE_URL}/auth/signup.php`,
  LOGOUT: `${API_BASE_URL}/auth/logout.php`,
  CHECK_SESSION: `${API_BASE_URL}/auth/check-session.php`,
  
  // Properties
  PROPERTIES: `${API_BASE_URL}/properties/index.php`,
  PROPERTY_BY_ID: (id: string) => `${API_BASE_URL}/properties/get.php?id=${id}`,
  
  // File Upload
  UPLOAD: `${API_BASE_URL}/upload/index.php`,
  
  // Contact
  CONTACT: `${API_BASE_URL}/contact/index.php`,

  // Blog
  BLOG_POSTS: `${API_BASE_URL}/blog/index.php`,
  BLOG_POST_BY_SLUG: (slug: string) => `${API_BASE_URL}/blog/get.php?slug=${slug}`,

  // User
  USER_PROPERTIES: `${API_BASE_URL}/user/properties.php`,
  USER_INQUIRIES: `${API_BASE_URL}/user/inquiries.php`,

  // Admin
  ADMIN_PROPERTIES: `${API_BASE_URL}/admin/properties.php`,
  APPROVE_PROPERTY: (id: string) => `${API_BASE_URL}/admin/approve.php?id=${id}`,
  REJECT_PROPERTY: (id: string) => `${API_BASE_URL}/admin/reject.php?id=${id}`,
};

// API helper functions
export const apiRequest = async (url: string, options: RequestInit = {}) => {
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include', // Include cookies for session management
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    console.log('🔍 API Request:', { url, options: mergedOptions });
    const response = await fetch(url, mergedOptions);
    console.log('🔍 API Response:', { status: response.status, ok: response.ok, headers: Object.fromEntries(response.headers.entries()) });

    const responseText = await response.text();
    console.log('🔍 Raw Response:', responseText);

    let data;
    try {
      data = JSON.parse(responseText);
      console.log('🔍 Parsed Data:', data);
    } catch (parseError) {
      console.error('🔍 JSON Parse Error:', parseError);
      throw new Error(`Invalid JSON response: ${responseText}`);
    }

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('🔍 API request failed:', error);
    throw error;
  }
};

// Authentication helpers
export const authAPI = {
  login: async (email: string, password: string) => {
    return apiRequest(API_ENDPOINTS.LOGIN, {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  },

  signup: async (name: string, email: string, password: string, phone?: string) => {
    return apiRequest(API_ENDPOINTS.SIGNUP, {
      method: 'POST',
      body: JSON.stringify({ name, email, password, phone }),
    });
  },

  logout: async () => {
    return apiRequest(API_ENDPOINTS.LOGOUT, {
      method: 'POST',
    });
  },

  checkSession: async () => {
    return apiRequest(API_ENDPOINTS.CHECK_SESSION);
  },
};

// Properties helpers
export const propertiesAPI = {
  getProperties: async (filters: Record<string, any> = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    
    const url = `${API_ENDPOINTS.PROPERTIES}?${queryParams.toString()}`;
    return apiRequest(url);
  },

  createProperty: async (propertyData: any) => {
    return apiRequest(API_ENDPOINTS.PROPERTIES, {
      method: 'POST',
      body: JSON.stringify(propertyData),
    });
  },

  getPropertyById: async (id: string) => {
    return apiRequest(API_ENDPOINTS.PROPERTY_BY_ID(id));
  },
};

// Upload helpers
export const uploadAPI = {
  uploadFile: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    return fetch(API_ENDPOINTS.UPLOAD, {
      method: 'POST',
      body: formData,
      credentials: 'include',
    }).then(response => {
      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`);
      }
      return response.json();
    });
  },
};

// Contact helpers
export const contactAPI = {
  sendMessage: async (name: string, email: string, message: string, phone?: string, type?: string) => {
    return apiRequest(API_ENDPOINTS.CONTACT, {
      method: 'POST',
      body: JSON.stringify({ name, email, message, phone, type }),
    });
  },
};

// Admin helpers
export const adminAPI = {
  getProperties: async (status?: string) => {
    const url = status ? `${API_ENDPOINTS.ADMIN_PROPERTIES}?status=${status}` : API_ENDPOINTS.ADMIN_PROPERTIES;
    return apiRequest(url);
  },

  getUsers: async () => {
    return apiRequest(`${API_BASE_URL}/admin/users.php`);
  },

  approveProperty: async (id: string) => {
    return apiRequest(API_ENDPOINTS.APPROVE_PROPERTY(id), {
      method: 'PUT',
    });
  },

  rejectProperty: async (id: string, reason?: string) => {
    return apiRequest(API_ENDPOINTS.REJECT_PROPERTY(id), {
      method: 'PUT',
      body: JSON.stringify({ reason }),
    });
  },
};

// Blog helpers
export const blogAPI = {
  getPosts: async (filters: Record<string, any> = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const url = `${API_ENDPOINTS.BLOG_POSTS}?${queryParams.toString()}`;
    return apiRequest(url);
  },

  getPostBySlug: async (slug: string) => {
    return apiRequest(API_ENDPOINTS.BLOG_POST_BY_SLUG(slug));
  },
};

// User helpers
export const userAPI = {
  getProperties: async () => {
    return apiRequest(API_ENDPOINTS.USER_PROPERTIES);
  },

  getInquiries: async () => {
    return apiRequest(API_ENDPOINTS.USER_INQUIRIES);
  },
};

export default {
  API_BASE_URL,
  API_ENDPOINTS,
  apiRequest,
  authAPI,
  propertiesAPI,
  uploadAPI,
  contactAPI,
  blogAPI,
  adminAPI,
  userAPI,
};
