<?php
require_once '../../config/database.php';

setCorsHeaders();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check admin authentication
    $session_token = $_COOKIE['session_token'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    
    if (!$session_token) {
        sendError('Authentication required', 401);
    }
    
    // Remove 'Bearer ' prefix if present
    if (strpos($session_token, 'Bearer ') === 0) {
        $session_token = substr($session_token, 7);
    }
    
    // Check if we're using SQLite or MySQL
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;
    
    // Verify admin session
    if ($isSQLite) {
        $auth_query = "SELECT u.role FROM UserSession us 
                       JOIN User u ON us.userId = u.id 
                       WHERE us.sessionToken = :token AND us.expiresAt > datetime('now')";
    } else {
        $auth_query = "SELECT u.role FROM user_sessions us 
                       JOIN users u ON us.user_id = u.id 
                       WHERE us.session_token = :token AND us.expires_at > NOW()";
    }
    
    $auth_stmt = $db->prepare($auth_query);
    $auth_stmt->bindParam(':token', $session_token);
    $auth_stmt->execute();
    $auth_result = $auth_stmt->fetch();
    
    if (!$auth_result || $auth_result['role'] !== 'ADMIN') {
        sendError('Admin access required', 403);
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get all users for admin
        if ($isSQLite) {
            $query = "SELECT id, name, email, role, phone, isActive as is_active, createdAt as created_at,
                             (SELECT COUNT(*) FROM Property WHERE ownerId = User.id) as properties_count
                      FROM User 
                      ORDER BY createdAt DESC";
        } else {
            $query = "SELECT id, name, email, role, phone, is_active, created_at,
                             (SELECT COUNT(*) FROM properties WHERE owner_id = users.id) as properties_count
                      FROM users 
                      ORDER BY created_at DESC";
        }
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $users = $stmt->fetchAll();
        
        sendResponse([
            'success' => true,
            'users' => $users
        ]);
        
    } else {
        sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Admin users API error: " . $e->getMessage());
    sendError('Failed to fetch users', 500);
}
?>
