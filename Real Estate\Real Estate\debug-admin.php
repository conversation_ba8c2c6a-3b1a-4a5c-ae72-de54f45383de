<?php
echo "🔍 Debugging Admin Login Issues...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database\n\n";
    
    // Check admin users
    $users = $db->query('SELECT id, email, password, role FROM User WHERE role = "ADMIN"')->fetchAll();
    
    echo "📋 Admin users found: " . count($users) . "\n\n";
    
    foreach ($users as $user) {
        echo "Email: " . $user['email'] . "\n";
        echo "Password hash: " . substr($user['password'], 0, 30) . "...\n";
        echo "Role: " . $user['role'] . "\n";
        
        // Test password verification
        $testPasswords = [
            'Admin@2024!' => '<EMAIL>',
            'new1234' => '<EMAIL>'
        ];
        
        foreach ($testPasswords as $password => $expectedEmail) {
            if ($user['email'] === $expectedEmail) {
                $verified = password_verify($password, $user['password']);
                echo "Password test ($password): " . ($verified ? 'SUCCESS ✅' : 'FAILED ❌') . "\n";
            }
        }
        echo "---\n";
    }
    
    // Check if API login endpoint exists
    echo "\n🔍 Checking API files:\n";
    $apiFiles = [
        'php-backend/api/auth/login.php' => 'Login API',
        'php-backend/config/database.php' => 'Database Config'
    ];
    
    foreach ($apiFiles as $file => $description) {
        if (file_exists($file)) {
            echo "✅ $description exists\n";
        } else {
            echo "❌ $description missing\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
