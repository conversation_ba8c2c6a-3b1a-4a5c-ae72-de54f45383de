<?php
// Test Login API Directly
echo "<h1>🔐 Test Login API</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";

echo "<h2>1. Test Admin Credentials in Database</h2>";
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->prepare('SELECT id, name, email, password, role, isActive FROM User WHERE email = ?');
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p class='success'>✅ Admin user found in database</p>";
        echo "<p><strong>Email:</strong> " . $admin['email'] . "</p>";
        echo "<p><strong>Role:</strong> " . $admin['role'] . "</p>";
        echo "<p><strong>Active:</strong> " . ($admin['isActive'] ? 'Yes' : 'No') . "</p>";
        
        // Test password
        $testPassword = 'Admin@2024!';
        if (password_verify($testPassword, $admin['password'])) {
            echo "<p class='success'>✅ Password verification successful</p>";
        } else {
            echo "<p class='error'>❌ Password verification failed</p>";
        }
    } else {
        echo "<p class='error'>❌ Admin user not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>2. Test Login API Endpoint</h2>";

// Simulate a login request
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'Admin@2024!'
];

echo "<p><strong>Testing login with:</strong></p>";
echo "<pre>" . json_encode($loginData, JSON_PRETTY_PRINT) . "</pre>";

// Test if login API file exists
if (file_exists('php-backend/api/auth/login.php')) {
    echo "<p class='success'>✅ Login API file exists</p>";
    
    // Check if we can access it
    $loginUrl = 'php-backend/api/auth/login.php';
    echo "<p><strong>API URL:</strong> <a href='$loginUrl' target='_blank'>$loginUrl</a></p>";
    
} else {
    echo "<p class='error'>❌ Login API file not found</p>";
}

echo "<h2>3. Manual Login Test</h2>";
echo "<p>Try logging in manually with these exact credentials:</p>";
echo "<div style='background:#e7f3ff;padding:15px;border-radius:5px;border-left:4px solid #007bff;'>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Password:</strong> Admin@2024!</p>";
echo "</div>";

echo "<p><a href='admin/login/' target='_blank' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Try Admin Login Again</a></p>";

echo "<h2>4. Alternative: Check Properties First</h2>";
echo "<p>Let's verify the main functionality is working:</p>";
echo "<p><a href='properties/' target='_blank' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Check Properties Page</a></p>";
echo "<p><a href='test-api-simple.php' style='background:#17a2b8;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Test Properties API</a></p>";

echo "<h2>5. Debug Info</h2>";
echo "<p>If login still fails, check:</p>";
echo "<ul>";
echo "<li>Browser console for JavaScript errors</li>";
echo "<li>Network tab to see API requests</li>";
echo "<li>Make sure you're typing the password exactly: <code>Admin@2024!</code></li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Generated: " . date('Y-m-d H:i:s') . "</small></p>";
?>
