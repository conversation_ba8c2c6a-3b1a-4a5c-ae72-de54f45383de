<?php
require_once '../../config/database.php';

setCorsHeaders();

$method = $_SERVER['REQUEST_METHOD'];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    switch ($method) {
        case 'GET':
            handleGetProperties($db);
            break;
        case 'POST':
            handleCreateProperty($db);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Properties API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGetProperties($db) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 12;
    $offset = ($page - 1) * $limit;

    // Check if we're using SQLite or MySQL
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;

    if ($isSQLite) {
        // SQLite database (development)
        $propertiesTable = 'Property';
        $usersTable = 'User';
        $where_conditions = ["p.approvalStatus = 'APPROVED'", "p.isApproved = 1", "p.isActive = 1"];
    } else {
        // MySQL database (production)
        $propertiesTable = 'properties';
        $usersTable = 'users';
        $where_conditions = ["p.approval_status = 'APPROVED'", "p.is_approved = 1", "p.is_active = 1"];
    }

    $params = [];
    
    // Search filter
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = '%' . $_GET['search'] . '%';
        $where_conditions[] = "(p.title LIKE :search OR p.description LIKE :search2 OR p.city LIKE :search3 OR p.state LIKE :search4)";
        $params[':search'] = $search;
        $params[':search2'] = $search;
        $params[':search3'] = $search;
        $params[':search4'] = $search;
    }
    
    // Type filter
    if (isset($_GET['type']) && !empty($_GET['type']) && $_GET['type'] !== 'all') {
        $where_conditions[] = "p.type = :type";
        $params[':type'] = $_GET['type'];
    }
    
    // Listing type filter
    if (isset($_GET['listingType']) && !empty($_GET['listingType']) && $_GET['listingType'] !== 'all') {
        $listingTypeField = $isSQLite ? 'p.listingType' : 'p.listing_type';
        $where_conditions[] = "$listingTypeField = :listing_type";
        $params[':listing_type'] = $_GET['listingType'];
    }
    
    // City filter
    if (isset($_GET['city']) && !empty($_GET['city']) && $_GET['city'] !== 'all') {
        $where_conditions[] = "p.city = :city";
        $params[':city'] = $_GET['city'];
    }
    
    // Bedrooms filter
    if (isset($_GET['bedrooms']) && !empty($_GET['bedrooms']) && $_GET['bedrooms'] !== 'all') {
        $where_conditions[] = "p.bedrooms = :bedrooms";
        $params[':bedrooms'] = (int)$_GET['bedrooms'];
    }
    
    // Price range filter
    if (isset($_GET['minPrice']) && !empty($_GET['minPrice'])) {
        $where_conditions[] = "p.price >= :min_price";
        $params[':min_price'] = (int)$_GET['minPrice'];
    }
    
    if (isset($_GET['maxPrice']) && !empty($_GET['maxPrice'])) {
        $where_conditions[] = "p.price <= :max_price";
        $params[':max_price'] = (int)$_GET['maxPrice'];
    }
    
    // PG-specific filters
    if (isset($_GET['roomType']) && !empty($_GET['roomType'])) {
        $roomTypeField = $isSQLite ? 'p.pgRoomType' : 'p.pg_room_type';
        $where_conditions[] = "$roomTypeField = :room_type";
        $params[':room_type'] = $_GET['roomType'];
    }

    if (isset($_GET['sharing']) && !empty($_GET['sharing'])) {
        $roomTypeField = $isSQLite ? 'p.pgRoomType' : 'p.pg_room_type';
        $where_conditions[] = "$roomTypeField = :sharing";
        $params[':sharing'] = $_GET['sharing'];
    }

    if (isset($_GET['foodIncluded']) && !empty($_GET['foodIncluded'])) {
        $food_value = $_GET['foodIncluded'] === 'YES' ? 1 : 0;
        $foodField = $isSQLite ? 'p.foodIncluded' : 'p.food_included';
        $where_conditions[] = "$foodField = :food_included";
        $params[':food_included'] = $food_value;
    }

    if (isset($_GET['gender']) && !empty($_GET['gender'])) {
        $genderField = $isSQLite ? 'p.pgGenderPreference' : 'p.pg_gender_preference';
        $where_conditions[] = "$genderField = :gender";
        $params[':gender'] = $_GET['gender'];
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM $propertiesTable p WHERE $where_clause";
    $count_stmt = $db->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total = $count_stmt->fetch()['total'];

    // Get properties with appropriate field names
    if ($isSQLite) {
        $ownerIdField = 'p.ownerId';
        $createdAtField = 'p.createdAt';
    } else {
        $ownerIdField = 'p.owner_id';
        $createdAtField = 'p.created_at';
    }

    $query = "SELECT p.*, u.name as owner_name, u.email as owner_email
              FROM $propertiesTable p
              JOIN $usersTable u ON $ownerIdField = u.id
              WHERE $where_clause
              ORDER BY $createdAtField DESC
              LIMIT :limit OFFSET :offset";
    
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $properties = $stmt->fetchAll();
    
    // Process properties data
    foreach ($properties as &$property) {
        $property['images'] = json_decode($property['images'], true) ?: [];
        $property['amenities'] = json_decode($property['amenities'], true) ?: [];
        $property['owner'] = [
            'name' => $property['owner_name'],
            'email' => $property['owner_email']
        ];
        unset($property['owner_name'], $property['owner_email']);
    }
    
    $total_pages = ceil($total / $limit);
    
    sendResponse([
        'properties' => $properties,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => $total_pages
        ]
    ]);
}

function handleCreateProperty($db) {
    // Check authentication
    $user = getCurrentUser($db);
    if (!$user) {
        sendError('Authentication required', 401);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $required_fields = ['title', 'description', 'price', 'type', 'listingType', 'address', 'city', 'state', 'pincode'];
    $input = validateInput($input, $required_fields);
    
    // Generate property ID
    $property_id = uniqid('prop_', true);
    
    // Prepare data
    $data = [
        'id' => $property_id,
        'title' => sanitizeInput($input['title']),
        'description' => sanitizeInput($input['description']),
        'price' => (int)$input['price'],
        'currency' => isset($input['currency']) ? sanitizeInput($input['currency']) : 'INR',
        'type' => sanitizeInput($input['type']),
        'listing_type' => sanitizeInput($input['listingType']),
        'bedrooms' => isset($input['bedrooms']) ? (int)$input['bedrooms'] : null,
        'bathrooms' => isset($input['bathrooms']) ? (int)$input['bathrooms'] : null,
        'area' => isset($input['area']) ? (int)$input['area'] : null,
        'address' => sanitizeInput($input['address']),
        'city' => sanitizeInput($input['city']),
        'state' => sanitizeInput($input['state']),
        'pincode' => sanitizeInput($input['pincode']),
        'images' => json_encode(isset($input['images']) ? $input['images'] : []),
        'amenities' => json_encode(isset($input['amenities']) ? $input['amenities'] : []),
        'owner_id' => $user['id'],
        'approval_status' => 'PENDING',
        'is_approved' => 0,
        'view_count' => 0
    ];
    
    // Add accommodation type for non-PG properties
    if ($input['type'] !== 'PG' && isset($input['accommodationType'])) {
        $data['accommodation_type'] = sanitizeInput($input['accommodationType']);
    }
    
    // Add PG-specific fields
    if ($input['type'] === 'PG') {
        if (isset($input['pgRoomType'])) {
            $data['pg_room_type'] = sanitizeInput($input['pgRoomType']);
        }
        if (isset($input['pgGenderPreference'])) {
            $data['pg_gender_preference'] = sanitizeInput($input['pgGenderPreference']);
        }
    }
    
    // Build INSERT query
    $columns = implode(', ', array_keys($data));
    $placeholders = ':' . implode(', :', array_keys($data));
    
    $query = "INSERT INTO properties ($columns) VALUES ($placeholders)";
    $stmt = $db->prepare($query);
    
    foreach ($data as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    
    if ($stmt->execute()) {
        sendResponse([
            'success' => true,
            'property' => array_merge($data, ['id' => $property_id])
        ], 201);
    } else {
        sendError('Failed to create property', 500);
    }
}

function getCurrentUser($db) {
    // Get session token
    $session_token = null;
    
    if (isset($_COOKIE['session_token'])) {
        $session_token = $_COOKIE['session_token'];
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $session_token = $matches[1];
        }
    }
    
    if (!$session_token) {
        return null;
    }
    
    // Check session
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
