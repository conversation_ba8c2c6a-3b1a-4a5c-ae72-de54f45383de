-- =====================================================
-- SINGLE COMPLETE DATABASE SETUP FOR PRODUCTION
-- housing.okayy.in Real Estate Website
-- MySQL/MariaDB Compatible
-- =====================================================

-- Drop existing tables if they exist (for clean setup)
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS saved_properties;
DROP TABLE IF EXISTS inquiries;
DROP TABLE IF EXISTS contact_messages;
DROP TABLE IF EXISTS blog_posts;
DROP TABLE IF EXISTS properties;
DROP TABLE IF EXISTS users;
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 1. USERS TABLE
-- =====================================================
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('USER', 'ADMIN') DEFAULT 'USER',
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role)
);

-- =====================================================
-- 2. PROPERTIES TABLE
-- =====================================================
CREATE TABLE properties (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    price INT NOT NULL,
    currency VARCHAR(10) DEFAULT 'INR',
    type ENUM('APARTMENT', 'HOUSE', 'VILLA', 'PLOT', 'COMMERCIAL', 'OFFICE', 'PG') NOT NULL,
    listing_type ENUM('RENT', 'SALE') DEFAULT 'SALE',
    accommodation_type ENUM('FULL_HOUSE', 'FLAT', 'ONE_BHK', 'TWO_BHK', 'THREE_BHK', 'FOUR_BHK', 'FAMILY', 'BACHELOR'),
    pg_room_type ENUM('SINGLE', 'DOUBLE', 'TRIPLE', 'FOUR_SHARING', 'DORMITORY'),
    pg_gender_preference ENUM('MALE', 'FEMALE', 'MIXED'),
    status ENUM('AVAILABLE', 'SOLD', 'RENTED', 'PENDING') DEFAULT 'AVAILABLE',
    bedrooms INT,
    bathrooms INT,
    area INT,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    images TEXT,
    amenities TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT FALSE,
    approval_status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'APPROVED',
    rejection_reason TEXT,
    admin_notes TEXT,
    view_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    owner_id VARCHAR(50) NOT NULL,
    approved_by VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    approved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_type (type),
    INDEX idx_city (city),
    INDEX idx_status (status),
    INDEX idx_approval (approval_status),
    INDEX idx_owner (owner_id),
    INDEX idx_featured (is_featured)
);

-- =====================================================
-- 3. BLOG POSTS TABLE
-- =====================================================
CREATE TABLE blog_posts (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image VARCHAR(500),
    published BOOLEAN DEFAULT TRUE,
    tags TEXT,
    category VARCHAR(100),
    author_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_published (published),
    INDEX idx_slug (slug),
    INDEX idx_category (category)
);

-- =====================================================
-- 4. OTHER TABLES
-- =====================================================
CREATE TABLE saved_properties (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    property_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    UNIQUE KEY unique_save (user_id, property_id)
);

CREATE TABLE inquiries (
    id VARCHAR(50) PRIMARY KEY,
    property_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    message TEXT,
    status ENUM('NEW', 'CONTACTED', 'QUALIFIED', 'CLOSED') DEFAULT 'NEW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE contact_messages (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    message TEXT NOT NULL,
    type ENUM('GENERAL', 'VALUATION', 'INQUIRY') DEFAULT 'GENERAL',
    status ENUM('NEW', 'READ', 'REPLIED') DEFAULT 'NEW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_sessions (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (session_token),
    INDEX idx_user (user_id)
);

-- =====================================================
-- 5. INSERT ADMIN USERS
-- =====================================================
INSERT INTO users (id, name, email, password, role, is_active, created_at, updated_at) VALUES
('admin-housing-main', 'Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ADMIN', TRUE, NOW(), NOW()),
('admin-okayy-second', 'Second Admin', '<EMAIL>', '$2y$10$N9qo8uLOickgx2ZMRZoMye/Lo2o7cFWUNv/ovrzI4cOOdQap.Ru.', 'ADMIN', TRUE, NOW(), NOW());

-- =====================================================
-- 6. INSERT SAMPLE PROPERTIES
-- =====================================================
INSERT INTO properties (
    id, title, description, price, currency, type, listing_type, accommodation_type,
    bedrooms, bathrooms, area, address, city, state, pincode,
    images, amenities, is_featured, is_approved, approval_status,
    view_count, is_active, owner_id, approved_by, created_at, approved_at
) VALUES
(
    'prop-luxury-bandra-001',
    'Luxury 3BHK Apartment in Bandra West',
    'Spacious 3BHK apartment with modern amenities, sea view, and prime location in Bandra West. Perfect for families looking for comfort and convenience.',
    25000000,
    'INR',
    'APARTMENT',
    'SALE',
    'FLAT',
    3,
    2,
    1200,
    '123 Hill Road, Bandra West',
    'Mumbai',
    'Maharashtra',
    '400050',
    '["https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=800", "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=800"]',
    '["Swimming Pool", "Gym", "Parking", "Security", "Elevator", "Garden"]',
    TRUE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-main',
    'admin-housing-main',
    NOW(),
    NOW()
),
(
    'prop-villa-gurgaon-002',
    'Modern 2BHK Villa in Gurgaon',
    'Beautiful 2BHK villa with garden, modern kitchen, and excellent connectivity to Delhi NCR. Ideal for small families.',
    8500000,
    'INR',
    'VILLA',
    'SALE',
    'FULL_HOUSE',
    2,
    2,
    1500,
    'Sector 45, DLF Phase 2',
    'Gurgaon',
    'Haryana',
    '122002',
    '["https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=800", "https://images.unsplash.com/photo-1570129477492-45c003edd2be?q=80&w=800"]',
    '["Garden", "Parking", "Security", "Power Backup"]',
    FALSE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-main',
    'admin-housing-main',
    NOW(),
    NOW()
),
(
    'prop-flat-bangalore-003',
    'Affordable 1BHK Flat for Rent',
    'Cozy 1BHK flat available for rent in a prime location. Fully furnished with all modern amenities.',
    25000,
    'INR',
    'APARTMENT',
    'RENT',
    'FLAT',
    1,
    1,
    600,
    'Koramangala 4th Block',
    'Bangalore',
    'Karnataka',
    '560034',
    '["https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=800", "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?q=80&w=800"]',
    '["Furnished", "WiFi", "Parking", "Security"]',
    FALSE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-main',
    'admin-housing-main',
    NOW(),
    NOW()
),
(
    'prop-pg-hsr-004',
    'Premium PG for Working Professionals',
    'Premium PG accommodation with all meals included. Perfect for working professionals and students.',
    15000,
    'INR',
    'PG',
    'RENT',
    NULL,
    1,
    1,
    200,
    'HSR Layout Sector 1',
    'Bangalore',
    'Karnataka',
    '560102',
    '["https://images.unsplash.com/photo-1555854877-bab0e564b8d5?q=80&w=800"]',
    '["Food Included", "WiFi", "Laundry", "AC", "Security"]',
    FALSE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-main',
    'admin-housing-main',
    NOW(),
    NOW()
);

-- =====================================================
-- 7. INSERT SAMPLE BLOG POSTS
-- =====================================================
INSERT INTO blog_posts (id, title, slug, content, excerpt, featured_image, published, tags, category, author_id) VALUES
(
    'blog-hyderabad-areas-001',
    'Top 10 Areas to Buy Property in Hyderabad 2024',
    'top-10-areas-buy-property-hyderabad-2024',
    '<h2>Best Investment Areas in Hyderabad</h2><p>Hyderabad has emerged as one of India''s most promising real estate markets. Here are the top 10 areas to consider for property investment in 2024:</p><h3>1. Gachibowli</h3><p>Known as the IT hub of Hyderabad, Gachibowli offers excellent connectivity and modern infrastructure.</p><h3>2. Kondapur</h3><p>A rapidly developing area with great potential for appreciation.</p><h3>3. Manikonda</h3><p>Affordable housing options with good connectivity to IT corridors.</p>',
    'Discover the most promising areas in Hyderabad for property investment in 2024. From IT hubs to emerging localities, find your perfect investment opportunity.',
    'https://images.unsplash.com/photo-1582407947304-fd86f028f716?q=80&w=800',
    TRUE,
    '["real estate", "hyderabad", "investment", "property"]',
    'Investment Guide',
    'admin-housing-main'
),
(
    'blog-registration-guide-002',
    'Complete Guide to Property Registration Process in India',
    'property-registration-process-india',
    '<h2>Understanding Property Registration in India</h2><p>Property registration is a crucial legal process that establishes your ownership rights. Here''s a comprehensive guide:</p><h3>Required Documents</h3><ul><li>Sale Deed</li><li>Property Title Documents</li><li>NOC from Society/Builder</li><li>Property Tax Receipts</li><li>Identity and Address Proof</li></ul>',
    'A step-by-step guide to property registration in India. Learn about required documents, fees, and the complete process.',
    'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?q=80&w=800',
    TRUE,
    '["property registration", "legal", "documentation", "india"]',
    'Legal Guide',
    'admin-housing-main'
),
(
    'blog-home-loan-tips-003',
    'Home Loan Tips: How to Get the Best Interest Rates in 2024',
    'home-loan-tips-best-interest-rates',
    '<h2>Getting the Best Home Loan Rates</h2><p>Securing a home loan with favorable interest rates can save you lakhs over the loan tenure. Here are expert tips:</p><h3>Improve Your Credit Score</h3><p>A credit score above 750 significantly improves your chances of getting better rates.</p><h3>Compare Multiple Lenders</h3><p>Don''t settle for the first offer. Compare rates from banks, NBFCs, and housing finance companies.</p>',
    'Expert tips to secure the best home loan interest rates in 2024. Learn how to save money on your home loan.',
    'https://images.unsplash.com/photo-**********-6726b3ff858f?q=80&w=800',
    TRUE,
    '["home loan", "interest rates", "finance", "tips"]',
    'Finance Guide',
    'admin-housing-main'
);

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================
SELECT 'Database setup completed successfully!' as status;
SELECT
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(*) FROM users WHERE role = 'ADMIN') as admin_users,
    (SELECT COUNT(*) FROM properties) as total_properties,
    (SELECT COUNT(*) FROM properties WHERE approval_status = 'APPROVED') as approved_properties,
    (SELECT COUNT(*) FROM blog_posts) as total_blog_posts;

-- Show admin users
SELECT 'Admin users created:' as info;
SELECT id, name, email, role, is_active FROM users WHERE role = 'ADMIN';

-- =====================================================
-- SETUP COMPLETE!
-- =====================================================
-- Admin Credentials:
-- 1. <EMAIL> | Password: Admin@2024!
-- 2. <EMAIL> | Password: new1234
--
-- Sample Data:
-- - 2 Admin users
-- - 4 Properties (all approved)
-- - 3 Blog posts
-- =====================================================
