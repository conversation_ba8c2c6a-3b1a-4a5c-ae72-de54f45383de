(()=>{var e={};e.id=116,e.ids=[116],e.modules={216:(e,t,r)=>{"use strict";r.d(t,{Eo:()=>d,M5:()=>i,R2:()=>n,hh:()=>l});let s="https://housing.okayy.in/php-backend/api";console.log("\uD83D\uDD0D API_BASE_URL:",s),console.log("\uD83D\uDD0D NODE_ENV:","production");let a={LOGIN:`${s}/auth/login.php`,SIGNUP:`${s}/auth/signup.php`,LOGOUT:`${s}/auth/logout.php`,CHECK_SESSION:`${s}/auth/check-session.php`,PROPERTIES:`${s}/properties/index.php`,PROPERTY_BY_ID:e=>`${s}/properties/get.php?id=${e}`,UPLOAD:`${s}/upload/index.php`,CONTACT:`${s}/contact/index.php`,BLOG_POSTS:`${s}/blog/index.php`,BLOG_POST_BY_SLUG:e=>`${s}/blog/get.php?slug=${e}`,USER_PROPERTIES:`${s}/user/properties.php`,USER_INQUIRIES:`${s}/user/inquiries.php`,ADMIN_PROPERTIES:`${s}/admin/properties.php`,APPROVE_PROPERTY:e=>`${s}/admin/approve.php?id=${e}`,REJECT_PROPERTY:e=>`${s}/admin/reject.php?id=${e}`},o=async(e,t={})=>{let r={headers:{"Content-Type":"application/json"},credentials:"include"},s={...r,...t,headers:{...r.headers,...t.headers}};try{let t;console.log("\uD83D\uDD0D API Request:",{url:e,options:s});let r=await fetch(e,s);console.log("\uD83D\uDD0D API Response:",{status:r.status,ok:r.ok,headers:Object.fromEntries(r.headers.entries())});let a=await r.text();console.log("\uD83D\uDD0D Raw Response:",a);try{t=JSON.parse(a),console.log("\uD83D\uDD0D Parsed Data:",t)}catch(e){throw console.error("\uD83D\uDD0D JSON Parse Error:",e),Error(`Invalid JSON response: ${a}`)}if(!r.ok)throw Error(t.error||`HTTP error! status: ${r.status}`);return t}catch(e){throw console.error("\uD83D\uDD0D API request failed:",e),e}},n={login:async(e,t)=>o(a.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:t})}),signup:async(e,t,r,s)=>o(a.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:t,password:r,phone:s})}),logout:async()=>o(a.LOGOUT,{method:"POST"}),checkSession:async()=>o(a.CHECK_SESSION)},i={getProperties:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&""!==r&&t.append(e,r.toString())}),o(`${a.PROPERTIES}?${t.toString()}`)},createProperty:async e=>o(a.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>o(a.PROPERTY_BY_ID(e))},l={getPosts:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&""!==r&&t.append(e,r.toString())}),o(`${a.BLOG_POSTS}?${t.toString()}`)},getPostBySlug:async e=>o(a.BLOG_POST_BY_SLUG(e))},d={getProperties:async()=>o(a.USER_PROPERTIES),getInquiries:async()=>o(a.USER_INQUIRIES)}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2744:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4248:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(687),a=r(3210),o=r(6189),n=r(5814),i=r.n(n),l=r(216);function d(){let[e,t]=(0,a.useState)(""),[r,n]=(0,a.useState)(""),[d,c]=(0,a.useState)(""),[p,u]=(0,a.useState)(!1),m=(0,o.useRouter)(),h=async t=>{t.preventDefault(),u(!0),c("");try{console.log("\uD83D\uDD0D Admin login attempt:",{email:e,passwordLength:r.length});let t=await l.R2.login(e,r);if(console.log("\uD83D\uDD0D Login response:",t),t.success){if("ADMIN"!==t.user.role){c("Access denied. Admin privileges required."),u(!1);return}localStorage.setItem("user",JSON.stringify(t.user)),m.push("/admin/dashboard")}else c("Login failed. Please try again.")}catch(e){console.error("Admin login error:",e),c(e.message||"Login failed. Please check your credentials.")}finally{u(!1)}};return(0,s.jsx)("main",{className:"min-h-screen bg-gray-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-red-600 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Admin Access"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-400",children:"Restricted area - Admin credentials required"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8",children:[(0,s.jsxs)("form",{className:"space-y-6",onSubmit:h,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Admin Email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm",placeholder:"Enter admin email",value:e,onChange:e=>t(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Admin Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm",placeholder:"Enter admin password",value:r,onChange:e=>n(e.target.value)})]}),d&&(0,s.jsx)("div",{className:"text-red-600 text-sm text-center bg-red-50 p-3 rounded-md",children:d}),(0,s.jsx)("div",{children:(0,s.jsxs)("button",{type:"submit",disabled:p,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("span",{className:"absolute left-0 inset-y-0 flex items-center pl-3",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-500 group-hover:text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"})})}),p?"Authenticating...":"Access Admin Panel"]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsx)(i(),{href:"/login",className:"text-sm text-gray-600 hover:text-gray-900",children:"← Back to regular login"})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"This area is restricted to authorized administrators only."})})]})})}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>n});var s=r(7413),a=r(5091),o=r.n(a);r(1135);let n={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function i({children:e}){return(0,s.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,s.jsx)("body",{className:`${o().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},5224:(e,t,r)=>{Promise.resolve().then(r.bind(r,4248))},5496:(e,t,r)=>{Promise.resolve().then(r.bind(r,6158))},6158:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\admin\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\login\\page.tsx","default")},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7287:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(5239),a=r(8088),o=r(8170),n=r.n(o),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6158)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\login\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/login/page",pathname:"/admin/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8031:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8279:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9592:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[771,814],()=>r(7287));module.exports=s})();