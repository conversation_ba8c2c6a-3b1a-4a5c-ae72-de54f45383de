<!DOCTYPE html>
<html>
<head>
    <title>Test Properties API</title>
</head>
<body>
    <h1>Test Properties API</h1>
    <button onclick="testPropertiesList()">Test Properties List</button>
    <button onclick="testPropertiesSearch()">Test Properties Search</button>
    <div id="result"></div>

    <script>
        async function testPropertiesList() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing properties list...';

            try {
                const response = await fetch('http://localhost:8000/php-backend/api/properties/index.php', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                console.log('Properties list response status:', response.status);
                const data = await response.json();
                console.log('Properties list response data:', data);

                resultDiv.innerHTML = `
                    <h3>Properties List Response:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Properties Count:</strong> ${data.properties ? data.properties.length : 0}</p>
                    ${data.properties && data.properties.length > 0 ? `
                        <p><strong>First Property:</strong> ${data.properties[0].title}</p>
                        <p><strong>Images:</strong> ${JSON.stringify(data.properties[0].images)}</p>
                    ` : ''}
                    <details>
                        <summary>Full Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;

            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        async function testPropertiesSearch() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing properties search...';

            try {
                const response = await fetch('http://localhost:8000/php-backend/api/properties/search.php?page=1&limit=5', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                console.log('Properties search response status:', response.status);
                const data = await response.json();
                console.log('Properties search response data:', data);

                resultDiv.innerHTML = `
                    <h3>Properties Search Response:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Properties Count:</strong> ${data.properties ? data.properties.length : 0}</p>
                    <p><strong>Total:</strong> ${data.pagination ? data.pagination.total : 'N/A'}</p>
                    ${data.properties && data.properties.length > 0 ? `
                        <p><strong>First Property:</strong> ${data.properties[0].title}</p>
                        <p><strong>Images:</strong> ${JSON.stringify(data.properties[0].images)}</p>
                    ` : ''}
                    <details>
                        <summary>Full Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;

            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
