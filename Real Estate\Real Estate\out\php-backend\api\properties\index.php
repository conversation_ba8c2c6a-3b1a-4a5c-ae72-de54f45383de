<?php
require_once '../../config/database.php';

setCorsHeaders();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get properties with filters
        $filters = [];
        $params = [];
        
        // Check if we're using SQLite or MySQL
        $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;
        
        if ($isSQLite) {
            // SQLite database (development)
            $query = "SELECT p.*, u.name as owner_name, u.email as owner_email 
                      FROM Property p 
                      LEFT JOIN User u ON p.ownerId = u.id 
                      WHERE p.isActive = 1";
        } else {
            // MySQL database (production)
            $query = "SELECT p.*, u.name as owner_name, u.email as owner_email 
                      FROM properties p 
                      LEFT JOIN users u ON p.owner_id = u.id 
                      WHERE p.is_active = 1";
        }
        
        // Add filters
        if (isset($_GET['type']) && !empty($_GET['type'])) {
            $query .= " AND p.type = :type";
            $params[':type'] = $_GET['type'];
        }
        
        if (isset($_GET['city']) && !empty($_GET['city'])) {
            $query .= " AND p.city LIKE :city";
            $params[':city'] = '%' . $_GET['city'] . '%';
        }
        
        if (isset($_GET['min_price']) && !empty($_GET['min_price'])) {
            $query .= " AND p.price >= :min_price";
            $params[':min_price'] = $_GET['min_price'];
        }
        
        if (isset($_GET['max_price']) && !empty($_GET['max_price'])) {
            $query .= " AND p.price <= :max_price";
            $params[':max_price'] = $_GET['max_price'];
        }
        
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            if ($isSQLite) {
                $query .= " AND p.approvalStatus = :status";
            } else {
                $query .= " AND p.approval_status = :status";
            }
            $params[':status'] = $_GET['status'];
        }
        
        // Add ordering
        if ($isSQLite) {
            $query .= " ORDER BY p.createdAt DESC";
        } else {
            $query .= " ORDER BY p.created_at DESC";
        }
        
        // Add pagination
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $query .= " LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $stmt = $db->prepare($query);
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }
        $stmt->execute();
        
        $properties = $stmt->fetchAll();
        
        // Get total count for pagination
        $countQuery = str_replace('SELECT p.*, u.name as owner_name, u.email as owner_email', 'SELECT COUNT(*)', $query);
        $countQuery = preg_replace('/ORDER BY.*$/', '', $countQuery);
        $countQuery = preg_replace('/LIMIT.*$/', '', $countQuery);
        
        $countStmt = $db->prepare($countQuery);
        foreach ($params as $key => $value) {
            if ($key !== ':limit' && $key !== ':offset') {
                $countStmt->bindValue($key, $value);
            }
        }
        $countStmt->execute();
        $total = $countStmt->fetchColumn();
        
        sendResponse([
            'success' => true,
            'properties' => $properties,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => (int)$total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } else {
        sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Properties API error: " . $e->getMessage());
    sendError('Failed to fetch properties', 500);
}
?>
