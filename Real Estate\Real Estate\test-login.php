<?php
echo "=== Testing Admin Login API ===\n";

// Test the login API endpoint
$url = 'http://localhost:8000/php-backend/api/auth/login.php';
$data = json_encode([
    'email' => '<EMAIL>',
    'password' => 'Admin@2024!'
]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($data)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n";

// Also test if the file exists
$loginFile = 'php-backend/api/auth/login.php';
if (file_exists($loginFile)) {
    echo "\nLogin file exists: $loginFile\n";
} else {
    echo "\nLogin file NOT found: $loginFile\n";
}

// Test direct inclusion
echo "\n=== Testing Direct Login Logic ===\n";
try {
    $_POST['email'] = '<EMAIL>';
    $_POST['password'] = 'Admin@2024!';
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // Capture output
    ob_start();
    include 'php-backend/api/auth/login.php';
    $output = ob_get_clean();
    
    echo "Direct output: $output\n";
} catch (Exception $e) {
    echo "Direct test error: " . $e->getMessage() . "\n";
}
?>
