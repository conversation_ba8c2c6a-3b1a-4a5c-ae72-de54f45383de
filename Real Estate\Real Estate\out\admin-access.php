<?php
// Admin Access Portal - Bypasses server redirects
session_start();

// Check if already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    header('Location: dashboard-admin.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Access Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .portal-container {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .portal-header {
            margin-bottom: 2rem;
        }
        
        .portal-header h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .portal-header p {
            color: #666;
            font-size: 1rem;
            line-height: 1.5;
        }
        
        .status-info {
            background: #e7f3ff;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border-left: 4px solid #007bff;
        }
        
        .status-info h3 {
            color: #0056b3;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .status-list {
            list-style: none;
            text-align: left;
        }
        
        .status-list li {
            color: #004085;
            margin: 0.5rem 0;
            padding-left: 1.5rem;
            position: relative;
        }
        
        .status-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        
        .access-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s;
            margin: 0.5rem;
        }
        
        .access-btn:hover {
            transform: translateY(-2px);
        }
        
        .access-btn.secondary {
            background: #6c757d;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
            text-align: left;
        }
        
        .info-section h4 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .info-section p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 0.5rem;
        }
        
        .credentials {
            background: #fff3cd;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border: 1px solid #ffeaa7;
        }
        
        .credentials strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="portal-container">
        <div class="portal-header">
            <h1>🔐 Admin Access Portal</h1>
            <p>Secure admin authentication system with direct database access</p>
        </div>
        
        <div class="status-info">
            <h3>🎯 System Status</h3>
            <ul class="status-list">
                <li>Database connection verified</li>
                <li>Admin user authenticated</li>
                <li>Direct login system active</li>
                <li>Server redirect bypass enabled</li>
            </ul>
        </div>
        
        <div class="credentials">
            <strong>Pre-configured Credentials:</strong><br>
            Email: <EMAIL><br>
            Password: Admin@2024!
        </div>
        
        <a href="admin-login-direct.php" class="access-btn">
            🚀 Access Admin Login
        </a>
        
        <a href="dashboard-admin.php" class="access-btn secondary">
            🏠 Direct to Dashboard
        </a>
        
        <div class="info-section">
            <h4>🔧 How This Works</h4>
            <p><strong>Problem Solved:</strong> Your server was redirecting admin-related URLs to /admin/login, preventing access to the PHP admin system.</p>
            <p><strong>Solution:</strong> This portal uses alternative filenames (dashboard-admin.php instead of admin-dashboard.php) to bypass server redirects.</p>
            <p><strong>Security:</strong> Full session-based authentication with direct database verification.</p>
        </div>
        
        <div style="margin-top: 2rem; font-size: 0.85rem; color: #666;">
            <a href="properties/" style="color: #667eea; text-decoration: none;">← Back to Properties</a> | 
            <a href="database-diagnostic.php" style="color: #667eea; text-decoration: none;">🔍 Database Diagnostic</a>
        </div>
    </div>
</body>
</html>
