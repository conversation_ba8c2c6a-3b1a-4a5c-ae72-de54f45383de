(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[990],{1008:(e,t,o)=>{"use strict";o.d(t,{Eo:()=>l,M5:()=>r,R2:()=>a,hh:()=>i});let s="https://housing.okayy.in/php-backend/api";console.log("\uD83D\uDD0D API_BASE_URL:",s),console.log("\uD83D\uDD0D NODE_ENV:","production");let n={LOGIN:"".concat(s,"/auth/login.php"),SIGNUP:"".concat(s,"/auth/signup.php"),LOGOUT:"".concat(s,"/auth/logout.php"),CHECK_SESSION:"".concat(s,"/auth/check-session.php"),PROPERTIES:"".concat(s,"/properties/index.php"),PROPERTY_BY_ID:e=>"".concat(s,"/properties/get.php?id=").concat(e),BLOG_POSTS:"".concat(s,"/blog/index.php"),BLOG_POST_BY_SLUG:e=>"".concat(s,"/blog/get.php?slug=").concat(e),USER_PROPERTIES:"".concat(s,"/user/properties.php"),USER_INQUIRIES:"".concat(s,"/user/inquiries.php")},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o={headers:{"Content-Type":"application/json"},credentials:"include"},s={...o,...t,headers:{...o.headers,...t.headers}};try{let t;console.log("\uD83D\uDD0D API Request:",{url:e,options:s});let o=await fetch(e,s);console.log("\uD83D\uDD0D API Response:",{status:o.status,ok:o.ok,headers:Object.fromEntries(o.headers.entries())});let n=await o.text();console.log("\uD83D\uDD0D Raw Response:",n);try{t=JSON.parse(n),console.log("\uD83D\uDD0D Parsed Data:",t)}catch(e){throw console.error("\uD83D\uDD0D JSON Parse Error:",e),Error("Invalid JSON response: ".concat(n))}if(!o.ok)throw Error(t.error||"HTTP error! status: ".concat(o.status));return t}catch(e){throw console.error("\uD83D\uDD0D API request failed:",e),e}},a={login:async(e,t)=>c(n.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:t})}),signup:async(e,t,o,s)=>c(n.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:t,password:o,phone:s})}),logout:async()=>c(n.LOGOUT,{method:"POST"}),checkSession:async()=>c(n.CHECK_SESSION)},r={getProperties:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[o,s]=e;null!=s&&""!==s&&t.append(o,s.toString())}),c("".concat(n.PROPERTIES,"?").concat(t.toString()))},createProperty:async e=>c(n.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>c(n.PROPERTY_BY_ID(e))},i={getPosts:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[o,s]=e;null!=s&&""!==s&&t.append(o,s.toString())}),c("".concat(n.BLOG_POSTS,"?").concat(t.toString()))},getPostBySlug:async e=>c(n.BLOG_POST_BY_SLUG(e))},l={getProperties:async()=>c(n.USER_PROPERTIES),getInquiries:async()=>c(n.USER_INQUIRIES)}},4523:(e,t,o)=>{Promise.resolve().then(o.bind(o,5040))},5040:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>a});var s=o(5155),n=o(2115),c=o(1008);function a(){let[e,t]=(0,n.useState)(""),[o,a]=(0,n.useState)(!1),r=async()=>{a(!0),t("Testing...");try{console.log("\uD83D\uDD0D Starting API test...");let e=await c.R2.login("<EMAIL>","Admin@2024!");console.log("\uD83D\uDD0D API test response:",e),t("✅ SUCCESS: ".concat(JSON.stringify(e,null,2)))}catch(e){console.error("\uD83D\uDD0D API test error:",e),t("❌ ERROR: ".concat(e.message))}finally{a(!1)}},i=async()=>{a(!0),t("Testing direct fetch...");try{console.log("\uD83D\uDD0D Starting direct fetch test...");let e=await fetch("http://localhost:8000/php-backend/api/auth/login.php",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:"<EMAIL>",password:"Admin@2024!"})});console.log("\uD83D\uDD0D Direct fetch response:",e);let o=await e.json();console.log("\uD83D\uDD0D Direct fetch data:",o),t("✅ DIRECT FETCH SUCCESS: ".concat(JSON.stringify(o,null,2)))}catch(e){console.error("\uD83D\uDD0D Direct fetch error:",e),t("❌ DIRECT FETCH ERROR: ".concat(e.message))}finally{a(!1)}};return(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"\uD83D\uDD27 API Test Page"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{onClick:r,disabled:o,className:"bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50",children:o?"Testing...":"Test authAPI.login()"}),(0,s.jsx)("button",{onClick:i,disabled:o,className:"bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50 ml-4",children:o?"Testing...":"Test Direct Fetch"})]}),(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Result:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded overflow-auto text-sm",children:e||"Click a button to test..."})]}),(0,s.jsx)("div",{className:"mt-4 text-sm text-gray-600",children:(0,s.jsx)("p",{children:"Open browser console (F12) to see detailed logs"})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(4523)),_N_E=e.O()}]);