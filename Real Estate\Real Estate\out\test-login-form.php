<?php
// Test Login Form - Direct PHP Login Test
echo "<h1>🔐 Direct Login Test</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f8f9fa;} 
.container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;}
.form-group{margin:15px 0;} label{display:block;margin-bottom:5px;font-weight:bold;}
input[type=email], input[type=password]{width:100%;padding:10px;border:1px solid #ddd;border-radius:5px;box-sizing:border-box;}
.btn{background:#007bff;color:white;padding:12px 24px;border:none;border-radius:5px;cursor:pointer;text-decoration:none;display:inline-block;}
.btn:hover{background:#0056b3;} .btn-success{background:#28a745;} .btn-danger{background:#dc3545;}
</style>";

echo "<div class='container'>";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
    echo "<h2>🔍 Testing Login...</h2>";
    
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    try {
        // Connect to database
        $db = new PDO('sqlite:prisma/dev.db');
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Find user
        $stmt = $db->prepare('SELECT id, name, email, password, role, isActive FROM User WHERE email = ?');
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            echo "<p class='error'>❌ User not found with email: $email</p>";
        } else if (!$user['isActive']) {
            echo "<p class='error'>❌ User account is deactivated</p>";
        } else if (!password_verify($password, $user['password'])) {
            echo "<p class='error'>❌ Invalid password</p>";
        } else {
            echo "<p class='success'>✅ Login successful!</p>";
            echo "<p><strong>User Details:</strong></p>";
            echo "<ul>";
            echo "<li>Name: " . htmlspecialchars($user['name']) . "</li>";
            echo "<li>Email: " . htmlspecialchars($user['email']) . "</li>";
            echo "<li>Role: " . htmlspecialchars($user['role']) . "</li>";
            echo "</ul>";
            
            echo "<h3>✅ Admin Login is Working!</h3>";
            echo "<p class='success'>The backend authentication is functioning correctly.</p>";
            echo "<p><strong>Issue:</strong> The frontend login form may not be properly sending requests to the backend API.</p>";
            
            echo "<h3>🔧 Next Steps:</h3>";
            echo "<ol>";
            echo "<li>Check if the properties page is working: <a href='properties/' target='_blank' class='btn btn-success'>Test Properties Page</a></li>";
            echo "<li>Try the admin login form again: <a href='admin/login/' target='_blank' class='btn'>Admin Login Form</a></li>";
            echo "<li>Check browser console for JavaScript errors when logging in</li>";
            echo "</ol>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// Show current admin credentials
echo "<h2>📋 Current Admin Credentials</h2>";
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $stmt = $db->prepare('SELECT name, email, role, isActive FROM User WHERE role = ?');
    $stmt->execute(['ADMIN']);
    $admins = $stmt->fetchAll();
    
    if ($admins) {
        echo "<p class='success'>✅ Found " . count($admins) . " admin user(s):</p>";
        foreach ($admins as $admin) {
            echo "<div style='background:#e7f3ff;padding:10px;margin:10px 0;border-radius:5px;'>";
            echo "<strong>Name:</strong> " . htmlspecialchars($admin['name']) . "<br>";
            echo "<strong>Email:</strong> " . htmlspecialchars($admin['email']) . "<br>";
            echo "<strong>Role:</strong> " . htmlspecialchars($admin['role']) . "<br>";
            echo "<strong>Active:</strong> " . ($admin['isActive'] ? 'Yes' : 'No') . "<br>";
            echo "</div>";
        }
    } else {
        echo "<p class='error'>❌ No admin users found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking admin users: " . $e->getMessage() . "</p>";
}

// Login test form
echo "<h2>🧪 Test Login Directly</h2>";
echo "<p>Use this form to test if the login credentials work:</p>";

echo "<form method='POST'>";
echo "<div class='form-group'>";
echo "<label for='email'>Admin Email:</label>";
echo "<input type='email' id='email' name='email' value='<EMAIL>' required>";
echo "</div>";

echo "<div class='form-group'>";
echo "<label for='password'>Admin Password:</label>";
echo "<input type='password' id='password' name='password' value='Admin@2024!' required>";
echo "</div>";

echo "<button type='submit' name='test_login' class='btn'>🔐 Test Login</button>";
echo "</form>";

echo "<h2>🔗 Quick Links</h2>";
echo "<p><a href='properties/' target='_blank' class='btn btn-success'>Check Properties Page</a></p>";
echo "<p><a href='admin/login/' target='_blank' class='btn'>Try Admin Login Form</a></p>";
echo "<p><a href='test-api-simple.php' class='btn btn-info'>Test Properties API</a></p>";

echo "</div>";

echo "<hr>";
echo "<p><small>Generated: " . date('Y-m-d H:i:s') . "</small></p>";
?>
