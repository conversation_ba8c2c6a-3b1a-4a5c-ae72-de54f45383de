(()=>{var e={};e.id=990,e.ids=[990],e.modules={216:(e,t,r)=>{"use strict";r.d(t,{Eo:()=>d,M5:()=>i,R2:()=>n,hh:()=>l});let s="/php-backend/api";console.log("\uD83D\uDD0D API_BASE_URL:",s),console.log("\uD83D\uDD0D NODE_ENV:","production");let a={LOGIN:`${s}/auth/login.php`,SIGNUP:`${s}/auth/signup.php`,LOGOUT:`${s}/auth/logout.php`,CHECK_SESSION:`${s}/auth/check-session.php`,PROPERTIES:`${s}/properties/index.php`,PROPERTY_BY_ID:e=>`${s}/properties/get.php?id=${e}`,UPLOAD:`${s}/upload/index.php`,CONTACT:`${s}/contact/index.php`,BLOG_POSTS:`${s}/blog/index.php`,BLOG_POST_BY_SLUG:e=>`${s}/blog/get.php?slug=${e}`,USER_PROPERTIES:`${s}/user/properties.php`,USER_INQUIRIES:`${s}/user/inquiries.php`,ADMIN_PROPERTIES:`${s}/admin/properties.php`,APPROVE_PROPERTY:e=>`${s}/admin/approve.php?id=${e}`,REJECT_PROPERTY:e=>`${s}/admin/reject.php?id=${e}`},o=async(e,t={})=>{let r={headers:{"Content-Type":"application/json"},credentials:"include"},s={...r,...t,headers:{...r.headers,...t.headers}};try{let t;console.log("\uD83D\uDD0D API Request:",{url:e,options:s});let r=await fetch(e,s);console.log("\uD83D\uDD0D API Response:",{status:r.status,ok:r.ok,headers:Object.fromEntries(r.headers.entries())});let a=await r.text();console.log("\uD83D\uDD0D Raw Response:",a);try{t=JSON.parse(a),console.log("\uD83D\uDD0D Parsed Data:",t)}catch(e){throw console.error("\uD83D\uDD0D JSON Parse Error:",e),Error(`Invalid JSON response: ${a}`)}if(!r.ok)throw Error(t.error||`HTTP error! status: ${r.status}`);return t}catch(e){throw console.error("\uD83D\uDD0D API request failed:",e),e}},n={login:async(e,t)=>o(a.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:t})}),signup:async(e,t,r,s)=>o(a.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:t,password:r,phone:s})}),logout:async()=>o(a.LOGOUT,{method:"POST"}),checkSession:async()=>o(a.CHECK_SESSION)},i={getProperties:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&""!==r&&t.append(e,r.toString())}),o(`${a.PROPERTIES}?${t.toString()}`)},createProperty:async e=>o(a.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>o(a.PROPERTY_BY_ID(e))},l={getPosts:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&""!==r&&t.append(e,r.toString())}),o(`${a.BLOG_POSTS}?${t.toString()}`)},getPostBySlug:async e=>o(a.BLOG_POST_BY_SLUG(e))},d={getProperties:async()=>o(a.USER_PROPERTIES),getInquiries:async()=>o(a.USER_INQUIRIES)}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},869:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>h,tree:()=>d});var s=r(5239),a=r(8088),o=r(8170),n=r.n(o),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["test-api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2022)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\test-api\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\test-api\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-api/page",pathname:"/test-api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1135:()=>{},2022:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\test-api\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\test-api\\page.tsx","default")},2744:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>n});var s=r(7413),a=r(5091),o=r.n(a);r(1135);let n={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function i({children:e}){return(0,s.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,s.jsx)("body",{className:`${o().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},7156:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(687),a=r(3210),o=r(216);function n(){let[e,t]=(0,a.useState)(""),[r,n]=(0,a.useState)(!1),i=async()=>{n(!0),t("Testing...");try{console.log("\uD83D\uDD0D Starting API test...");let e=await o.R2.login("<EMAIL>","Admin@2024!");console.log("\uD83D\uDD0D API test response:",e),t(`✅ SUCCESS: ${JSON.stringify(e,null,2)}`)}catch(e){console.error("\uD83D\uDD0D API test error:",e),t(`❌ ERROR: ${e.message}`)}finally{n(!1)}},l=async()=>{n(!0),t("Testing direct fetch...");try{console.log("\uD83D\uDD0D Starting direct fetch test...");let e=await fetch("http://localhost:8000/php-backend/api/auth/login.php",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:"<EMAIL>",password:"Admin@2024!"})});console.log("\uD83D\uDD0D Direct fetch response:",e);let r=await e.json();console.log("\uD83D\uDD0D Direct fetch data:",r),t(`✅ DIRECT FETCH SUCCESS: ${JSON.stringify(r,null,2)}`)}catch(e){console.error("\uD83D\uDD0D Direct fetch error:",e),t(`❌ DIRECT FETCH ERROR: ${e.message}`)}finally{n(!1)}};return(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"\uD83D\uDD27 API Test Page"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{onClick:i,disabled:r,className:"bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50",children:r?"Testing...":"Test authAPI.login()"}),(0,s.jsx)("button",{onClick:l,disabled:r,className:"bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50 ml-4",children:r?"Testing...":"Test Direct Fetch"})]}),(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Result:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded overflow-auto text-sm",children:e||"Click a button to test..."})]}),(0,s.jsx)("div",{className:"mt-4 text-sm text-gray-600",children:(0,s.jsx)("p",{children:"Open browser console (F12) to see detailed logs"})})]})}},8031:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8119:(e,t,r)=>{Promise.resolve().then(r.bind(r,7156))},8279:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9389:(e,t,r)=>{Promise.resolve().then(r.bind(r,2022))},9592:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[771],()=>r(869));module.exports=s})();