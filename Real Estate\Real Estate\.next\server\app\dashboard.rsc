1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[894,[],"ClientPageRoot"]
5:I[4879,["874","static/chunks/874-8e9a565f7eb17c9e.js","494","static/chunks/494-447b4237fe03dae5.js","821","static/chunks/821-633469a9cf2fe713.js","105","static/chunks/app/dashboard/page-f51522f6340d946b.js"],"default"]
8:I[9665,[],"OutletBoundary"]
b:I[4911,[],"AsyncMetadataOutlet"]
d:I[9665,[],"ViewportBoundary"]
f:I[9665,[],"MetadataBoundary"]
11:I[6614,[],""]
:HL["/_next/static/css/8e07018fcf634ae8.css","style"]
0:{"P":null,"b":"R-i5RMCD4kyZjTrlfZAe9","p":"","c":["","dashboard",""],"i":false,"f":[[["",{"children":["dashboard",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/8e07018fcf634ae8.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"scroll-smooth","children":["$","body",null,{"className":"__variable_e8ce0c font-sans bg-background text-text-primary antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L4",null,{"Component":"$5","searchParams":{},"params":{},"promises":["$@6","$@7"]}],null,["$","$L8",null,{"children":["$L9","$La",["$","$Lb",null,{"promise":"$@c"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","6gsreGmfK2muYMZU03qXVv",{"children":[["$","$Ld",null,{"children":"$Le"}],null]}],["$","$Lf",null,{"children":"$L10"}]]}],false]],"m":"$undefined","G":["$11","$undefined"],"s":false,"S":true}
12:"$Sreact.suspense"
13:I[4911,[],"AsyncMetadata"]
6:{}
7:{}
10:["$","div",null,{"hidden":true,"children":["$","$12",null,{"fallback":null,"children":["$","$L13",null,{"promise":"$@14"}]}]}]
a:null
e:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
9:null
c:{"metadata":[["$","title","0",{"children":"Real Estate India - Buy, Sell, and Rent Properties"}],["$","meta","1",{"name":"description","content":"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs."}],["$","meta","2",{"name":"author","content":"Real Estate India"}],["$","meta","3",{"name":"keywords","content":"real estate India,property for sale,property for rent,buy property,sell property,apartments,houses,villas,commercial property"}],["$","meta","4",{"name":"creator","content":"Real Estate India"}],["$","meta","5",{"name":"publisher","content":"Real Estate India"}],["$","meta","6",{"name":"robots","content":"index, follow"}],["$","meta","7",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","8",{"rel":"canonical","href":"https://realestate-india.com/"}],["$","meta","9",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","10",{"property":"og:title","content":"Real Estate India - Buy, Sell, and Rent Properties"}],["$","meta","11",{"property":"og:description","content":"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities."}],["$","meta","12",{"property":"og:url","content":"https://realestate-india.com/"}],["$","meta","13",{"property":"og:site_name","content":"Real Estate India"}],["$","meta","14",{"property":"og:locale","content":"en_IN"}],["$","meta","15",{"property":"og:type","content":"website"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@realestateindia"}],["$","meta","18",{"name":"twitter:title","content":"Real Estate India - Buy, Sell, and Rent Properties"}],["$","meta","19",{"name":"twitter:description","content":"Find your dream home in India with our comprehensive real estate platform."}]],"error":null,"digest":"$undefined"}
14:{"metadata":"$c:metadata","error":null,"digest":"$undefined"}
