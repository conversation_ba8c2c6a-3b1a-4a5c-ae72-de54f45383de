<?php
// Test Properties API Endpoints
// Upload this file to test if the properties API is working correctly

echo "<h1>🧪 Properties API Test</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";

echo "<h2>📋 Testing API Endpoints</h2>";

// Test 1: Check if search.php exists
echo "<h3>1. Testing search.php endpoint</h3>";
if (file_exists('php-backend/api/properties/search.php')) {
    echo "<p class='success'>✅ search.php file exists</p>";
    
    // Test the API call
    $testUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/php-backend/api/properties/search.php?limit=4";
    echo "<p><strong>Test URL:</strong> <a href='$testUrl' target='_blank'>$testUrl</a></p>";
    
    // Make a test request
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Content-Type: application/json',
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents($testUrl, false, $context);
    
    if ($response !== false) {
        echo "<p class='success'>✅ API call successful</p>";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "<p class='success'>✅ Valid JSON response</p>";
            echo "<p><strong>Properties found:</strong> " . (isset($data['properties']) ? count($data['properties']) : 0) . "</p>";
            
            if (isset($data['properties']) && count($data['properties']) > 0) {
                echo "<h4>📋 Sample Property Data:</h4>";
                $sampleProperty = $data['properties'][0];
                echo "<pre>";
                echo "ID: " . ($sampleProperty['id'] ?? 'N/A') . "\n";
                echo "Title: " . ($sampleProperty['title'] ?? 'N/A') . "\n";
                echo "Price: " . ($sampleProperty['price'] ?? 'N/A') . "\n";
                echo "Type: " . ($sampleProperty['type'] ?? 'N/A') . "\n";
                echo "Images: " . ($sampleProperty['images'] ?? 'N/A') . "\n";
                echo "Address: " . ($sampleProperty['address'] ?? 'N/A') . "\n";
                echo "</pre>";
                
                // Test image URL processing
                if (isset($sampleProperty['images'])) {
                    $images = json_decode($sampleProperty['images'], true);
                    if ($images && count($images) > 0) {
                        $firstImage = $images[0];
                        echo "<h4>🖼️ Image URL Test:</h4>";
                        echo "<p><strong>Raw image path:</strong> $firstImage</p>";
                        
                        // Process image URL like the frontend does
                        if (strpos($firstImage, 'http') === 0) {
                            $processedUrl = $firstImage;
                        } elseif (strpos($firstImage, '/') === 0) {
                            $processedUrl = "https://housing.okayy.in" . $firstImage;
                        } else {
                            $processedUrl = "https://housing.okayy.in/php-backend/uploads/" . $firstImage;
                        }
                        
                        echo "<p><strong>Processed URL:</strong> <a href='$processedUrl' target='_blank'>$processedUrl</a></p>";
                        
                        // Check if image file exists
                        if (strpos($firstImage, '/') !== 0 && strpos($firstImage, 'http') !== 0) {
                            $localImagePath = "php-backend/uploads/" . $firstImage;
                            if (file_exists($localImagePath)) {
                                echo "<p class='success'>✅ Image file exists locally</p>";
                            } else {
                                echo "<p class='error'>❌ Image file not found locally: $localImagePath</p>";
                            }
                        }
                    }
                }
            } else {
                echo "<p class='error'>❌ No properties found in response</p>";
            }
            
            echo "<h4>📊 Full API Response:</h4>";
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<p class='error'>❌ Invalid JSON response</p>";
            echo "<p><strong>Raw response:</strong></p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<p class='error'>❌ API call failed</p>";
        $error = error_get_last();
        if ($error) {
            echo "<p class='error'>Error: " . $error['message'] . "</p>";
        }
    }
} else {
    echo "<p class='error'>❌ search.php file not found</p>";
}

// Test 2: Check database
echo "<h3>2. Testing Database Connection</h3>";
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Count properties
    $stmt = $db->query('SELECT COUNT(*) as count FROM Property');
    $result = $stmt->fetch();
    echo "<p><strong>Total properties in database:</strong> " . $result['count'] . "</p>";
    
    // Check approved properties
    $stmt = $db->query('SELECT COUNT(*) as count FROM Property WHERE approval_status = "APPROVED"');
    $result = $stmt->fetch();
    echo "<p><strong>Approved properties:</strong> " . $result['count'] . "</p>";
    
    // Sample property with images
    $stmt = $db->query('SELECT id, title, images FROM Property WHERE images IS NOT NULL AND images != "" AND images != "[]" LIMIT 1');
    $property = $stmt->fetch();
    if ($property) {
        echo "<p class='success'>✅ Found property with images</p>";
        echo "<p><strong>Sample:</strong> " . $property['title'] . "</p>";
        echo "<p><strong>Images JSON:</strong> " . $property['images'] . "</p>";
    } else {
        echo "<p class='error'>❌ No properties with images found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 3: Check uploads directory
echo "<h3>3. Testing Uploads Directory</h3>";
if (is_dir('php-backend/uploads')) {
    echo "<p class='success'>✅ Uploads directory exists</p>";
    
    $files = scandir('php-backend/uploads');
    $imageFiles = array_filter($files, function($file) {
        return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    });
    
    echo "<p><strong>Image files found:</strong> " . count($imageFiles) . "</p>";
    
    if (count($imageFiles) > 0) {
        echo "<p class='success'>✅ Sample images:</p>";
        echo "<ul>";
        foreach (array_slice($imageFiles, 0, 5) as $file) {
            $fileUrl = "https://housing.okayy.in/php-backend/uploads/" . $file;
            echo "<li><a href='$fileUrl' target='_blank'>$file</a></li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p class='error'>❌ Uploads directory not found</p>";
}

echo "<h2>🎯 Summary</h2>";
echo "<p>This test helps identify why property images and details might not be showing on the frontend.</p>";
echo "<p><strong>Common issues:</strong></p>";
echo "<ul>";
echo "<li>API endpoint not working (search.php missing or broken)</li>";
echo "<li>Database not properly uploaded or configured</li>";
echo "<li>No approved properties in database</li>";
echo "<li>Image files not uploaded to uploads directory</li>";
echo "<li>Incorrect image URL processing</li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Generated on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
