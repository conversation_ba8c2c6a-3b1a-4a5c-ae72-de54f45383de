(()=>{var e={};e.id=334,e.ids=[334],e.modules={546:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\pg\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\page.tsx","default")},821:(e,t,s)=>{Promise.resolve().then(s.bind(s,546))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1997:(e,t,s)=>{"use strict";s.d(t,{y:()=>n});var r=s(687),a=s(5814),i=s.n(a);function l(e,t=!0){let s=t?"₹":"";if(e>=1e7){let t=e/1e7;return`${s}${t.toFixed(+(t%1!=0))} Cr`}if(e>=1e5){let t=e/1e5;return`${s}${t.toFixed(+(t%1!=0))} L`}if(!(e>=1e3))return`${s}${e.toLocaleString("en-IN")}`;{let t=e/1e3;return`${s}${t.toFixed(+(t%1!=0))}K`}}function n({property:e}){var t,s,a,n;let o=(e=>{try{return JSON.parse(e||"[]")}catch{return[]}})(e.images),c=o.length>0&&(t=o[0])?t.startsWith("http")?t:t.startsWith("/")?`https://housing.okayy.in${t}`:`https://housing.okayy.in/php-backend/uploads/${t}`:"https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image",d=(e=>{let t=new Date(e);return 7>=Math.ceil(Math.abs(new Date().getTime()-t.getTime())/864e5)})(e.createdAt);return(0,r.jsxs)("div",{className:"card-elevated group overflow-hidden animate-fade-in",children:[(0,r.jsxs)("div",{className:"relative h-72 w-full overflow-hidden",children:[(0,r.jsx)("img",{src:c,alt:e.title,className:"w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110",onError:e=>{e.currentTarget.src="https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image"}}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsx)("div",{className:"absolute top-4 left-4",children:(0,r.jsx)("span",{className:`px-4 py-2 rounded-xl text-sm font-semibold backdrop-blur-sm border border-white/20 ${"PG"===e.type||e.title.toLowerCase().includes("pg")?"bg-purple-500/90 text-white":e.title.toLowerCase().includes("rent")||e.price<1e5?"bg-accent-500/90 text-white":"bg-primary-500/90 text-white"} shadow-soft`,children:"PG"===e.type||e.title.toLowerCase().includes("pg")?"PG":e.title.toLowerCase().includes("rent")||e.price<1e5?"For Rent":"For Sale"})}),d&&(0,r.jsx)("div",{className:"absolute top-4 right-16",children:(0,r.jsx)("span",{className:"px-4 py-2 rounded-xl text-sm font-semibold bg-success-500/90 text-white backdrop-blur-sm border border-white/20 shadow-soft animate-bounce-subtle",children:"✨ New"})}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300",children:(0,r.jsx)("button",{className:"w-full py-3 bg-white/95 backdrop-blur-sm text-text-primary font-semibold rounded-xl shadow-soft hover:bg-white transition-all duration-300",children:"Quick View"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-text-primary group-hover:text-primary-600 transition-colors duration-300 line-clamp-2",children:e.title}),(0,r.jsx)("p",{className:"text-text-tertiary text-sm flex items-center",children:(0,r.jsxs)("span",{className:"line-clamp-1",children:[e.address,", ",e.city,", ",e.state]})})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("span",{className:"text-2xl font-bold text-gradient",children:(s=e.price,a=e.title,n=e.type,a.toLowerCase().includes("rent")||"PG"===n||a.toLowerCase().includes("pg")||a.toLowerCase().includes("paying guest")||s<1e5?`${l(s)}/month`:l(s))}),(e.title.toLowerCase().includes("rent")||"PG"===e.type||e.title.toLowerCase().includes("pg")||e.price<1e5)&&(0,r.jsx)("p",{className:"text-xs text-text-tertiary",children:"per month"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-text-tertiary",children:"Price per sqft"}),(0,r.jsxs)("div",{className:"text-lg font-semibold text-text-secondary",children:["₹",Math.round(e.price/e.area).toLocaleString("en-IN")]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 py-4 border-t border-gray-100",children:[e.bedrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:e.bedrooms})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Beds"})]}),e.bathrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:e.bathrooms})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Baths"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-purple-600",children:e.area})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"sqft"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(i(),{href:"PG"===e.type?`/pg/${e.id}`:`/properties/${e.id}`,className:"btn-primary flex-1 text-center",children:"View Details"}),(0,r.jsx)("button",{className:"px-4 py-3 bg-primary-50 text-primary-600 font-semibold rounded-xl hover:bg-primary-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Call"})]})]})]},e.id)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4573:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(5239),a=s(8088),i=s(8170),l=s.n(i),n=s(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c={children:["",{children:["pg",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,546)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/pg/page",pathname:"/pg",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4741:(e,t,s)=>{Promise.resolve().then(s.bind(s,6418))},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},6418:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(687),a=s(3210),i=s(6189),l=s(9190),n=s(1317),o=s(1997);function c({onFilterChange:e}){(0,i.useSearchParams)();let[t,s]=(0,a.useState)({city:"",priceRange:"",gender:"",roomType:"",amenities:[]}),[l,n]=(0,a.useState)({location:!0,price:!0,gender:!0,roomType:!0,amenities:!0}),o=e=>{n(t=>({...t,[e]:!t[e]}))},c=e=>{s(t=>({...t,amenities:t.amenities.includes(e)?t.amenities.filter(t=>t!==e):[...t.amenities,e]}))};return(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-soft border border-gray-100 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-text-primary",children:"Filter PG Options"}),(0,r.jsx)("button",{onClick:()=>{s({city:"",priceRange:"",gender:"",roomType:"",amenities:[]})},className:"text-sm text-primary-600 hover:text-primary-700 font-medium",children:"Clear All"})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>o("location"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Location"}),(0,r.jsx)("svg",{className:`h-5 w-5 transform transition-transform ${l.location?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),l.location&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("input",{type:"text",placeholder:"Enter city or area",value:t.city,onChange:e=>s(t=>({...t,city:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["Hyderabad","Gachibowli","Hitech City","Madhapur","Kondapur","Kukatpally"].map(e=>(0,r.jsx)("button",{onClick:()=>s(t=>({...t,city:e})),className:`px-3 py-1 text-sm rounded-full border transition-colors ${t.city===e?"bg-primary-600 text-white border-primary-600":"bg-white text-text-secondary border-gray-300 hover:border-primary-600"}`,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>o("price"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Monthly Rent"}),(0,r.jsx)("svg",{className:`h-5 w-5 transform transition-transform ${l.price?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),l.price&&(0,r.jsx)("div",{className:"space-y-2",children:[{value:"",label:"Any Budget"},{value:"0-5000",label:"Under ₹5,000"},{value:"5000-10000",label:"₹5,000 - ₹10,000"},{value:"10000-15000",label:"₹10,000 - ₹15,000"},{value:"15000-20000",label:"₹15,000 - ₹20,000"},{value:"20000-",label:"Above ₹20,000"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",name:"priceRange",value:e.value,checked:t.priceRange===e.value,onChange:e=>s(t=>({...t,priceRange:e.target.value})),className:"form-radio h-4 w-4 text-primary-600"}),(0,r.jsx)("span",{className:"text-sm text-text-secondary",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>o("gender"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Gender Preference"}),(0,r.jsx)("svg",{className:`h-5 w-5 transform transition-transform ${l.gender?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),l.gender&&(0,r.jsx)("div",{className:"space-y-2",children:[{value:"",label:"Any"},{value:"male",label:"Male Only"},{value:"female",label:"Female Only"},{value:"mixed",label:"Co-ed"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",name:"gender",value:e.value,checked:t.gender===e.value,onChange:e=>s(t=>({...t,gender:e.target.value})),className:"form-radio h-4 w-4 text-primary-600"}),(0,r.jsx)("span",{className:"text-sm text-text-secondary",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>o("roomType"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Room Type"}),(0,r.jsx)("svg",{className:`h-5 w-5 transform transition-transform ${l.roomType?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),l.roomType&&(0,r.jsx)("div",{className:"space-y-2",children:[{value:"",label:"Any Type"},{value:"single",label:"Single Occupancy"},{value:"double",label:"Double Sharing"},{value:"triple",label:"Triple Sharing"},{value:"dormitory",label:"Dormitory"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",name:"roomType",value:e.value,checked:t.roomType===e.value,onChange:e=>s(t=>({...t,roomType:e.target.value})),className:"form-radio h-4 w-4 text-primary-600"}),(0,r.jsx)("span",{className:"text-sm text-text-secondary",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>o("amenities"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Amenities"}),(0,r.jsx)("svg",{className:`h-5 w-5 transform transition-transform ${l.amenities?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),l.amenities&&(0,r.jsx)("div",{className:"space-y-2",children:["WiFi","AC","Food Included","Laundry","Parking","Security","Power Backup","Water Supply","Gym","Common Area","TV","Fridge"].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:t.amenities.includes(e),onChange:()=>c(e),className:"form-checkbox h-4 w-4 text-primary-600 rounded"}),(0,r.jsx)("span",{className:"text-sm text-text-secondary",children:e})]},e))})]})]})}function d(){(0,i.useSearchParams)();let[e,t]=(0,a.useState)([]),[s,l]=(0,a.useState)(!0),[n,d]=(0,a.useState)({currentPage:1,totalPages:1,totalItems:0,hasNext:!1,hasPrev:!1}),[m,x]=(0,a.useState)({city:"",priceRange:"",gender:"",roomType:"",amenities:[]}),p=async(e=m,s=1)=>{l(!0);try{let r=Object.entries(e).reduce((e,[t,s])=>(s&&"string"==typeof s&&""!==s.trim()&&(e[t]=s),e),{}),a=new URLSearchParams({page:s.toString(),limit:"12",type:"PG",...r}).toString(),i=await fetch(`/php-backend/api/properties/search.php?${a}`);if(!i.ok)throw Error(`HTTP error! status: ${i.status}`);let l=await i.json();t(l.properties),d(l.pagination)}catch(e){console.error("Error loading PG properties:",e)}finally{l(!1)}};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("section",{className:"py-12",children:(0,r.jsx)("div",{className:"container-custom",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-1/4",children:(0,r.jsx)("div",{className:"sticky top-6",children:(0,r.jsx)(c,{onFilterChange:e=>{x(e),p(e,1)}})})}),(0,r.jsxs)("div",{className:"lg:w-3/4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-text-primary",children:"Available PG Accommodations"}),(0,r.jsxs)("p",{className:"text-text-secondary",children:[e.length," PG",1!==e.length?"s":""," found"]})]}),s?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-200 h-48 rounded-t-xl"}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-b-xl",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2"})]})]},t))}):e.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsx)(o.y,{property:e},e.id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-text-primary mb-2",children:"No PG accommodations found"}),(0,r.jsx)("p",{className:"text-text-secondary mb-6",children:"Try adjusting your filters or check back later for new listings."}),(0,r.jsx)("button",{onClick:()=>x({city:"",priceRange:"",gender:"",roomType:"",amenities:[]}),className:"btn-primary",children:"Clear Filters"})]})]})]})})})})}function m(){return(0,r.jsxs)("main",{className:"min-h-screen bg-background",children:[(0,r.jsx)(l.Navbar,{}),(0,r.jsxs)("section",{className:"relative bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600 text-white py-20",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,r.jsx)("div",{className:"relative container-custom",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold mb-6",children:"Find Your Perfect PG"}),(0,r.jsx)("p",{className:"text-xl md:text-2xl mb-8 text-white/90",children:"Discover comfortable and affordable paying guest accommodations in Hyderabad"}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Verified PG Owners"]}),(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Safe & Secure"]}),(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"All Amenities"]})]})]})})]}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"py-12 container-custom",children:(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"animate-pulse w-full max-w-4xl",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded mb-4 w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-200 h-48 rounded-t-xl"}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-b-xl",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2"})]})]},t))})]})})}),children:(0,r.jsx)(d,{})}),(0,r.jsx)(n.w,{})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[771,814,436,317],()=>s(4573));module.exports=r})();