<?php
// Production Diagnostics Script
// Upload this file to your production server to diagnose issues

echo "<h1>🔍 Production Environment Diagnostics</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";

echo "<h2>📋 System Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";

echo "<h2>🗂️ File Structure Check</h2>";

// Check if key directories exist
$directories = [
    'php-backend',
    'php-backend/api',
    'php-backend/api/auth',
    'php-backend/config',
    'prisma',
    'uploads'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        echo "<p class='success'>✅ Directory exists: $dir</p>";
    } else {
        echo "<p class='error'>❌ Directory missing: $dir</p>";
    }
}

// Check if key files exist
$files = [
    'php-backend/config/database.php',
    'php-backend/api/auth/login.php',
    'prisma/dev.db',
    'prisma/schema.prisma'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ File exists: $file</p>";
        echo "<p>   Size: " . filesize($file) . " bytes</p>";
    } else {
        echo "<p class='error'>❌ File missing: $file</p>";
    }
}

echo "<h2>🗄️ Database Connection Test</h2>";

try {
    // Test database connection
    if (file_exists('prisma/dev.db')) {
        $db = new PDO('sqlite:prisma/dev.db');
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Database connection successful</p>";
        
        // Check tables
        echo "<h3>📊 Database Tables</h3>";
        $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
        foreach ($tables as $table) {
            echo "<p>📋 Table: " . $table['name'] . "</p>";
        }
        
        // Check User table structure
        if (in_array('User', array_column($tables, 'name'))) {
            echo "<h3>👤 User Table Structure</h3>";
            $columns = $db->query("PRAGMA table_info(User)")->fetchAll();
            echo "<pre>";
            foreach ($columns as $column) {
                echo "Column: {$column['name']} | Type: {$column['type']} | Not Null: {$column['notnull']}\n";
            }
            echo "</pre>";
            
            // Check admin users
            echo "<h3>🔐 Admin Users Check</h3>";
            $adminUsers = $db->query("SELECT id, name, email, role, isActive FROM User WHERE role = 'ADMIN'")->fetchAll();
            if (empty($adminUsers)) {
                echo "<p class='error'>❌ No admin users found!</p>";
                
                // Create admin user
                echo "<h3>🛠️ Creating Admin User</h3>";
                try {
                    $hashedPassword = password_hash('Admin@2024!', PASSWORD_DEFAULT);
                    $adminId = 'admin_' . uniqid();
                    $now = date('Y-m-d H:i:s');
                    
                    $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
                    $stmt->execute([
                        $adminId,
                        'Admin User',
                        '<EMAIL>',
                        $hashedPassword,
                        'ADMIN',
                        1,
                        $now,
                        $now
                    ]);
                    
                    echo "<p class='success'>✅ Admin user created successfully!</p>";
                    echo "<p><strong>Email:</strong> <EMAIL></p>";
                    echo "<p><strong>Password:</strong> Admin@2024!</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>❌ Failed to create admin user: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p class='success'>✅ Found " . count($adminUsers) . " admin user(s):</p>";
                foreach ($adminUsers as $admin) {
                    echo "<p>👤 {$admin['name']} ({$admin['email']}) - Active: " . ($admin['isActive'] ? 'Yes' : 'No') . "</p>";
                }
            }
        } else {
            echo "<p class='error'>❌ User table not found!</p>";
        }
        
    } else {
        echo "<p class='error'>❌ Database file not found: prisma/dev.db</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>🔧 PHP Extensions Check</h2>";

$required_extensions = ['pdo', 'pdo_sqlite', 'json', 'fileinfo'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p class='success'>✅ Extension loaded: $ext</p>";
    } else {
        echo "<p class='error'>❌ Extension missing: $ext</p>";
    }
}

echo "<h2>📝 File Permissions Check</h2>";

$permission_checks = [
    'php-backend' => '755',
    'php-backend/uploads' => '755',
    'prisma' => '755',
    'uploads' => '755'
];

foreach ($permission_checks as $path => $expected) {
    if (file_exists($path)) {
        $perms = substr(sprintf('%o', fileperms($path)), -3);
        if ($perms >= $expected) {
            echo "<p class='success'>✅ $path permissions: $perms (OK)</p>";
        } else {
            echo "<p class='warning'>⚠️ $path permissions: $perms (should be $expected or higher)</p>";
        }
    }
}

echo "<h2>🌐 API Test</h2>";

// Test login API
if (file_exists('php-backend/api/auth/login.php')) {
    echo "<p class='success'>✅ Login API file exists</p>";
    echo "<p>🔗 API URL: " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/php-backend/api/auth/login.php</p>";
} else {
    echo "<p class='error'>❌ Login API file missing</p>";
}

echo "<h2>📋 Summary</h2>";
echo "<p>This diagnostic script helps identify issues with your production deployment.</p>";
echo "<p>If you see any red ❌ errors above, those need to be fixed for the admin login to work.</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>Fix any missing files or directories</li>";
echo "<li>Ensure database permissions are correct</li>";
echo "<li>Verify admin user exists in database</li>";
echo "<li>Test the login API directly</li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Generated on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
