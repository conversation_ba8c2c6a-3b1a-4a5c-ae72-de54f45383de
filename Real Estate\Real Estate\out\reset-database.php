<?php
// Quick Database Reset Script
// This script completely resets the database with fresh schema and sample data

echo "<h1>🔄 Database Reset</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;}</style>";

echo "<div style='background:#fff3cd;border:1px solid #ffeaa7;padding:15px;border-radius:5px;margin:20px 0;'>";
echo "<h3>⚠️ Warning</h3>";
echo "<p>This will completely reset your database and remove all existing data!</p>";
echo "<p>Only proceed if you want to start fresh with sample data.</p>";
echo "</div>";

// Check if user confirmed the reset
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<p><strong>To proceed with database reset, click the button below:</strong></p>";
    echo "<p><a href='?confirm=yes' style='background:#dc3545;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>⚠️ RESET DATABASE</a></p>";
    echo "<p><a href='verify-deployment.php' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin-left:10px;'>← Back to Verification</a></p>";
    exit;
}

try {
    echo "<p class='warning'>🔄 Starting database reset...</p>";
    
    // Remove existing database file
    if (file_exists('prisma/dev.db')) {
        unlink('prisma/dev.db');
        echo "<p class='success'>✅ Removed old database file</p>";
    }
    
    // Create fresh database
    if (!is_dir('prisma')) {
        mkdir('prisma', 0755, true);
    }
    
    touch('prisma/dev.db');
    chmod('prisma/dev.db', 0666);
    echo "<p class='success'>✅ Created fresh database file</p>";
    
    // Connect and create schema
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create User table
    $db->exec("
        CREATE TABLE User (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            phone TEXT,
            role TEXT NOT NULL DEFAULT 'USER',
            isActive INTEGER NOT NULL DEFAULT 1,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL
        )
    ");
    echo "<p class='success'>✅ Created User table</p>";
    
    // Create Property table
    $db->exec("
        CREATE TABLE Property (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            price INTEGER NOT NULL,
            currency TEXT NOT NULL DEFAULT 'INR',
            type TEXT NOT NULL,
            listingType TEXT NOT NULL DEFAULT 'SALE',
            accommodationType TEXT,
            pgRoomType TEXT,
            pgGenderPreference TEXT,
            status TEXT NOT NULL DEFAULT 'AVAILABLE',
            bedrooms INTEGER,
            bathrooms INTEGER,
            area INTEGER,
            address TEXT NOT NULL,
            city TEXT NOT NULL,
            state TEXT NOT NULL,
            pincode TEXT NOT NULL,
            latitude REAL,
            longitude REAL,
            images TEXT,
            amenities TEXT,
            is_featured INTEGER NOT NULL DEFAULT 0,
            is_approved INTEGER NOT NULL DEFAULT 1,
            approval_status TEXT NOT NULL DEFAULT 'APPROVED',
            view_count INTEGER NOT NULL DEFAULT 0,
            isActive INTEGER NOT NULL DEFAULT 1,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            approvedAt TEXT,
            approvedBy TEXT,
            ownerId TEXT NOT NULL,
            FOREIGN KEY (ownerId) REFERENCES User(id)
        )
    ");
    echo "<p class='success'>✅ Created Property table</p>";
    
    // Create admin user
    $hashedPassword = password_hash('Admin@2024!', PASSWORD_DEFAULT);
    $adminId = 'admin_' . uniqid();
    $now = date('Y-m-d H:i:s');
    
    $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
    $stmt->execute([$adminId, 'Admin User', '<EMAIL>', $hashedPassword, 'ADMIN', 1, $now, $now]);
    echo "<p class='success'>✅ Created admin user</p>";
    
    // Add sample properties
    $properties = [
        ['Luxury 3BHK Apartment in Bandra', 'Beautiful apartment with sea view', 8500000, 'APARTMENT', 'SALE', 3, 2, 1200, 'Bandra West, Mumbai', 'Mumbai', 'Maharashtra', '400050'],
        ['Affordable 1BHK Flat for Rent', 'Cozy flat in peaceful area', 25000, 'APARTMENT', 'RENT', 1, 1, 600, 'Andheri East, Mumbai', 'Mumbai', 'Maharashtra', '400069'],
        ['Premium PG for Professionals', 'Fully furnished PG with all amenities', 15000, 'PG', 'RENT', null, null, 150, 'Powai, Mumbai', 'Mumbai', 'Maharashtra', '400076'],
        ['Spacious 4BHK Villa', 'Independent villa with garden', 15000000, 'VILLA', 'SALE', 4, 3, 2500, 'Juhu, Mumbai', 'Mumbai', 'Maharashtra', '400049']
    ];
    
    foreach ($properties as $prop) {
        $propertyId = 'prop_' . uniqid();
        $stmt = $db->prepare('
            INSERT INTO Property (id, title, description, price, type, listingType, bedrooms, bathrooms, area, address, city, state, pincode, approval_status, isActive, createdAt, updatedAt, approvedAt, ownerId)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ');
        $stmt->execute([$propertyId, $prop[0], $prop[1], $prop[2], $prop[3], $prop[4], $prop[5], $prop[6], $prop[7], $prop[8], $prop[9], $prop[10], $prop[11], 'APPROVED', 1, $now, $now, $now, $adminId]);
    }
    echo "<p class='success'>✅ Added " . count($properties) . " sample properties</p>";
    
    echo "<h2>✅ Database Reset Complete!</h2>";
    echo "<p class='success'>Fresh database created with sample data.</p>";
    echo "<p><strong>Admin Login:</strong> <EMAIL> / Admin@2024!</p>";
    echo "<p><a href='properties/' target='_blank' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>View Properties</a></p>";
    echo "<p><a href='verify-deployment.php' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin-left:10px;'>Run Verification</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
