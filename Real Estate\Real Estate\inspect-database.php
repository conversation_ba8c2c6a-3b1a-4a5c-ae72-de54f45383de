<?php
// Database Inspection Script
// Upload this to check the database structure and data

echo "<h1>🔍 Database Inspection</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;}</style>";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Database connected successfully</p>";
    
    // Check Property table structure
    echo "<h2>📋 Property Table Structure</h2>";
    $columns = $db->query("PRAGMA table_info(Property)")->fetchAll();
    echo "<table>";
    echo "<tr><th>Column</th><th>Type</th><th>Not Null</th><th>Default</th><th>Primary Key</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['name']}</td>";
        echo "<td>{$column['type']}</td>";
        echo "<td>" . ($column['notnull'] ? 'Yes' : 'No') . "</td>";
        echo "<td>{$column['dflt_value']}</td>";
        echo "<td>" . ($column['pk'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Property statistics
    echo "<h2>📊 Property Statistics</h2>";
    
    // Total count
    $stmt = $db->query('SELECT COUNT(*) as count FROM Property');
    $total = $stmt->fetch()['count'];
    echo "<p><strong>Total Properties:</strong> $total</p>";
    
    if ($total > 0) {
        // By approval status
        echo "<h3>By Approval Status:</h3>";
        $stmt = $db->query('SELECT approval_status, COUNT(*) as count FROM Property GROUP BY approval_status');
        $statuses = $stmt->fetchAll();
        foreach ($statuses as $status) {
            echo "<p>• {$status['approval_status']}: {$status['count']}</p>";
        }
        
        // By type
        echo "<h3>By Property Type:</h3>";
        $stmt = $db->query('SELECT type, COUNT(*) as count FROM Property GROUP BY type');
        $types = $stmt->fetchAll();
        foreach ($types as $type) {
            echo "<p>• {$type['type']}: {$type['count']}</p>";
        }
        
        // By listing type
        echo "<h3>By Listing Type:</h3>";
        $stmt = $db->query('SELECT listingType, COUNT(*) as count FROM Property GROUP BY listingType');
        $listingTypes = $stmt->fetchAll();
        foreach ($listingTypes as $listingType) {
            echo "<p>• {$listingType['listingType']}: {$listingType['count']}</p>";
        }
        
        // Properties with images
        echo "<h3>Image Status:</h3>";
        $stmt = $db->query('SELECT COUNT(*) as count FROM Property WHERE images IS NOT NULL AND images != "" AND images != "[]"');
        $withImages = $stmt->fetch()['count'];
        echo "<p>• With Images: $withImages</p>";
        echo "<p>• Without Images: " . ($total - $withImages) . "</p>";
        
        // Sample properties
        echo "<h2>📋 Sample Properties</h2>";
        $stmt = $db->query('SELECT id, title, price, type, listingType, approval_status, images, address, city FROM Property LIMIT 5');
        $properties = $stmt->fetchAll();
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Title</th><th>Price</th><th>Type</th><th>Listing</th><th>Status</th><th>Images</th><th>Location</th></tr>";
        foreach ($properties as $property) {
            echo "<tr>";
            echo "<td>" . substr($property['id'], 0, 8) . "...</td>";
            echo "<td>" . htmlspecialchars(substr($property['title'], 0, 30)) . "...</td>";
            echo "<td>₹" . number_format($property['price']) . "</td>";
            echo "<td>{$property['type']}</td>";
            echo "<td>{$property['listingType']}</td>";
            echo "<td>{$property['approval_status']}</td>";
            
            // Parse images
            $images = json_decode($property['images'] ?? '[]', true);
            $imageCount = is_array($images) ? count($images) : 0;
            echo "<td>$imageCount images</td>";
            
            echo "<td>" . htmlspecialchars($property['city']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for approved properties specifically
        echo "<h2>✅ Approved Properties</h2>";
        $stmt = $db->query('SELECT COUNT(*) as count FROM Property WHERE approval_status = "APPROVED"');
        $approvedCount = $stmt->fetch()['count'];
        
        if ($approvedCount > 0) {
            echo "<p class='success'>✅ Found $approvedCount approved properties</p>";
            
            // Show sample approved properties
            $stmt = $db->query('SELECT id, title, price, type, images FROM Property WHERE approval_status = "APPROVED" LIMIT 3');
            $approvedProperties = $stmt->fetchAll();
            
            echo "<h3>Sample Approved Properties:</h3>";
            foreach ($approvedProperties as $property) {
                echo "<div style='border:1px solid #ddd;padding:10px;margin:10px 0;'>";
                echo "<h4>" . htmlspecialchars($property['title']) . "</h4>";
                echo "<p><strong>Price:</strong> ₹" . number_format($property['price']) . "</p>";
                echo "<p><strong>Type:</strong> {$property['type']}</p>";
                
                $images = json_decode($property['images'] ?? '[]', true);
                if (is_array($images) && count($images) > 0) {
                    echo "<p><strong>Images:</strong> " . count($images) . " found</p>";
                    echo "<p><strong>First image:</strong> " . htmlspecialchars($images[0]) . "</p>";
                } else {
                    echo "<p><strong>Images:</strong> None</p>";
                }
                echo "</div>";
            }
        } else {
            echo "<p class='error'>❌ No approved properties found!</p>";
            echo "<p>This is likely why the properties page is empty. Properties need to be approved to show on the frontend.</p>";
            
            // Show pending properties
            $stmt = $db->query('SELECT COUNT(*) as count FROM Property WHERE approval_status = "PENDING"');
            $pendingCount = $stmt->fetch()['count'];
            
            if ($pendingCount > 0) {
                echo "<p class='info'>ℹ️ Found $pendingCount pending properties that need approval</p>";
                
                // Quick approve all properties
                echo "<h3>🛠️ Quick Fix: Approve All Properties</h3>";
                echo "<p>Click the button below to approve all pending properties:</p>";
                
                if (isset($_POST['approve_all'])) {
                    try {
                        $stmt = $db->prepare('UPDATE Property SET approval_status = "APPROVED", approvedAt = ? WHERE approval_status = "PENDING"');
                        $stmt->execute([date('Y-m-d H:i:s')]);
                        $affected = $stmt->rowCount();
                        echo "<p class='success'>✅ Approved $affected properties!</p>";
                        echo "<p>Refresh the page to see updated statistics.</p>";
                    } catch (Exception $e) {
                        echo "<p class='error'>❌ Error approving properties: " . $e->getMessage() . "</p>";
                    }
                } else {
                    echo "<form method='post'>";
                    echo "<button type='submit' name='approve_all' style='background:#28a745;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;'>Approve All Pending Properties</button>";
                    echo "</form>";
                }
            }
        }
        
    } else {
        echo "<p class='error'>❌ No properties found in database!</p>";
        echo "<p>The database is empty. You need to add some properties first.</p>";
    }
    
    // Check User table for admin
    echo "<h2>👤 Admin Users</h2>";
    $stmt = $db->query('SELECT COUNT(*) as count FROM User WHERE role = "ADMIN"');
    $adminCount = $stmt->fetch()['count'];
    
    if ($adminCount > 0) {
        echo "<p class='success'>✅ Found $adminCount admin user(s)</p>";
        $stmt = $db->query('SELECT name, email, isActive FROM User WHERE role = "ADMIN"');
        $admins = $stmt->fetchAll();
        foreach ($admins as $admin) {
            echo "<p>• {$admin['name']} ({$admin['email']}) - " . ($admin['isActive'] ? 'Active' : 'Inactive') . "</p>";
        }
    } else {
        echo "<p class='error'>❌ No admin users found!</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    echo "<p>Make sure the database file exists and has proper permissions.</p>";
}

echo "<hr>";
echo "<p><small>Generated on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
