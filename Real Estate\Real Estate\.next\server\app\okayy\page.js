(()=>{var e={};e.id=606,e.ids=[606],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2744:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3887:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\okayy\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\okayy\\page.tsx","default")},4431:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o,metadata:()=>l});var t=r(7413),a=r(5091),i=r.n(a);r(1135);let l={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function o({children:e}){return(0,t.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,t.jsx)("body",{className:`${i().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},5018:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(687),a=r(3210),i=r(5814),l=r.n(i);function o(){let[e,s]=(0,a.useState)(0),r=[{id:"real-estate",title:"Real Estate",description:"Find your dream home or investment property",image:"/images/real-estate-hero.jpg",icon:"\uD83C\uDFE0",color:"from-blue-500 to-blue-600",link:"/",features:["Buy Properties","Rent Homes","PG Accommodation","Sell Property"]},{id:"groceries",title:"Groceries",description:"Fresh groceries delivered to your doorstep",image:"/images/groceries-hero.jpg",icon:"\uD83D\uDED2",color:"from-green-500 to-green-600",link:"/groceries",features:["Fresh Vegetables","Daily Essentials","Organic Products","Quick Delivery"]},{id:"jobs",title:"Jobs",description:"Discover career opportunities that match your skills",image:"/images/jobs-hero.jpg",icon:"\uD83D\uDCBC",color:"from-purple-500 to-purple-600",link:"/jobs",features:["Job Search","Career Guidance","Resume Builder","Interview Prep"]},{id:"tools",title:"Online Tools",description:"Powerful tools to boost your productivity",image:"/images/tools-hero.jpg",icon:"\uD83D\uDEE0️",color:"from-orange-500 to-orange-600",link:"/tools",features:["PDF Tools","Image Editor","Calculators","Converters"]}];return(0,t.jsxs)("main",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-white",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)(l(),{href:"/okayy",className:"text-2xl font-bold text-gray-900",children:["Okayy",(0,t.jsx)("span",{className:"text-blue-600",children:".in"})]})}),(0,t.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,t.jsx)(l(),{href:"#services",className:"text-gray-600 hover:text-gray-900",children:"Services"}),(0,t.jsx)(l(),{href:"#about",className:"text-gray-600 hover:text-gray-900",children:"About"}),(0,t.jsx)(l(),{href:"#contact",className:"text-gray-600 hover:text-gray-900",children:"Contact"})]})]})})}),(0,t.jsx)("section",{className:"relative py-20 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,t.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Your Gateway to",(0,t.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600",children:"Everything You Need"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"From finding your dream home to getting groceries delivered, discovering career opportunities, and accessing powerful online tools - all in one place."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(l(),{href:"#services",className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors",children:"Explore Services"}),(0,t.jsx)(l(),{href:"#about",className:"border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors",children:"Learn More"})]})]})}),(0,t.jsx)("section",{id:"services",className:"py-20 px-4 sm:px-6 lg:px-8 bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Our Services"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Comprehensive solutions for your daily needs, all under one roof"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:r.map((r,a)=>(0,t.jsxs)("div",{className:`group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:-translate-y-2 ${e===a?"ring-2 ring-blue-500":""}`,onClick:()=>s(a),children:[(0,t.jsx)("div",{className:`h-48 bg-gradient-to-br ${r.color} flex items-center justify-center`,children:(0,t.jsx)("span",{className:"text-6xl",children:r.icon})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:r.title}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:r.description}),(0,t.jsx)("ul",{className:"space-y-1 mb-4",children:r.features.map((e,s)=>(0,t.jsxs)("li",{className:"text-sm text-gray-500 flex items-center",children:[(0,t.jsx)("span",{className:"w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"}),e]},s))}),(0,t.jsxs)(l(),{href:r.link,className:`inline-flex items-center text-white bg-gradient-to-r ${r.color} px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300`,children:["Explore ",r.title,(0,t.jsx)("svg",{className:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},r.id))}),(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2",children:[(0,t.jsxs)("div",{className:"p-8 lg:p-12",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("span",{className:"text-4xl mr-4",children:r[e].icon}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:r[e].title})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-6 text-lg",children:r[e].description}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-8",children:r[e].features.map((s,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`w-3 h-3 bg-gradient-to-r ${r[e].color} rounded-full mr-3`}),(0,t.jsx)("span",{className:"text-gray-700",children:s})]},a))}),(0,t.jsxs)(l(),{href:r[e].link,className:`inline-flex items-center text-white bg-gradient-to-r ${r[e].color} px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300`,children:["Get Started",(0,t.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]})]}),(0,t.jsx)("div",{className:`bg-gradient-to-br ${r[e].color} flex items-center justify-center min-h-[300px]`,children:(0,t.jsx)("span",{className:"text-8xl opacity-20",children:r[e].icon})})]})})]})}),(0,t.jsx)("section",{className:"py-16 px-4 sm:px-6 lg:px-8 bg-white",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:"10K+"}),(0,t.jsx)("div",{className:"text-gray-600",children:"Properties Listed"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-2",children:"5K+"}),(0,t.jsx)("div",{className:"text-gray-600",children:"Grocery Orders"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:"2K+"}),(0,t.jsx)("div",{className:"text-gray-600",children:"Jobs Posted"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-orange-600 mb-2",children:"50+"}),(0,t.jsx)("div",{className:"text-gray-600",children:"Online Tools"})]})]})})}),(0,t.jsx)("footer",{className:"bg-gray-900 text-white py-16 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"md:col-span-1",children:[(0,t.jsxs)(l(),{href:"/okayy",className:"text-2xl font-bold mb-4 block",children:["Okayy",(0,t.jsx)("span",{className:"text-blue-400",children:".in"})]}),(0,t.jsx)("p",{className:"text-gray-400 mb-4",children:"Your one-stop destination for real estate, groceries, jobs, and online tools."}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"})})}),(0,t.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"})})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Services"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/",className:"text-gray-400 hover:text-white",children:"Real Estate"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/groceries",className:"text-gray-400 hover:text-white",children:"Groceries"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/jobs",className:"text-gray-400 hover:text-white",children:"Jobs"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/tools",className:"text-gray-400 hover:text-white",children:"Online Tools"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Company"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/about",className:"text-gray-400 hover:text-white",children:"About Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/contact",className:"text-gray-400 hover:text-white",children:"Contact"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/careers",className:"text-gray-400 hover:text-white",children:"Careers"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/privacy",className:"text-gray-400 hover:text-white",children:"Privacy Policy"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Contact"}),(0,t.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,t.jsx)("li",{children:"\uD83D\uDCE7 <EMAIL>"}),(0,t.jsx)("li",{children:"\uD83D\uDCDE +91 9876543210"}),(0,t.jsx)("li",{children:"\uD83D\uDCCD Hyderabad, India"})]})]})]}),(0,t.jsx)("div",{className:"border-t border-gray-800 mt-12 pt-8 text-center text-gray-400",children:(0,t.jsx)("p",{children:"\xa9 2024 Okayy.in. All rights reserved."})})]})})]})}},5405:(e,s,r)=>{Promise.resolve().then(r.bind(r,3887))},7857:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(5239),a=r(8088),i=r(8170),l=r.n(i),o=r(893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(s,n);let d={children:["",{children:["okayy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3887)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\okayy\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\okayy\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/okayy/page",pathname:"/okayy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8031:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8279:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9373:(e,s,r)=>{Promise.resolve().then(r.bind(r,5018))},9592:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[771,814],()=>r(7857));module.exports=t})();