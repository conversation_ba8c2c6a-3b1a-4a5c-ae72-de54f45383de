<?php
// Database Diagnostic Tool
echo "<!DOCTYPE html><html><head><title>Database Diagnostic</title>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f8f9fa;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:5px;margin:5px 0;} 
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:5px;margin:5px 0;} 
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:5px;margin:5px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:5px;margin:5px 0;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
.btn{background:#007bff;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;text-decoration:none;display:inline-block;margin:5px;}
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔍 Database Diagnostic Tool</h1>";

try {
    // Test database connection
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Check database file
    $dbPath = 'prisma/dev.db';
    if (file_exists($dbPath)) {
        $size = filesize($dbPath);
        echo "<div class='info'>📁 Database file: $dbPath (Size: " . number_format($size) . " bytes)</div>";
    } else {
        echo "<div class='error'>❌ Database file not found: $dbPath</div>";
    }
    
    // List all tables
    echo "<h2>📋 Database Tables</h2>";
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<div class='warning'>⚠️ No tables found in database</div>";
    } else {
        echo "<div class='success'>Found " . count($tables) . " tables:</div>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li><strong>$table</strong></li>";
        }
        echo "</ul>";
    }
    
    // Analyze each table
    foreach ($tables as $tableName) {
        echo "<h3>🔍 Table: $tableName</h3>";
        
        // Get table structure
        $columns = $db->query("PRAGMA table_info($tableName)")->fetchAll(PDO::FETCH_ASSOC);
        echo "<h4>Columns:</h4>";
        echo "<table>";
        echo "<tr><th>Name</th><th>Type</th><th>Not Null</th><th>Default</th><th>Primary Key</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($col['name']) . "</td>";
            echo "<td>" . htmlspecialchars($col['type']) . "</td>";
            echo "<td>" . ($col['notnull'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . htmlspecialchars($col['dflt_value'] ?? 'NULL') . "</td>";
            echo "<td>" . ($col['pk'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Get row count
        try {
            $count = $db->query("SELECT COUNT(*) FROM $tableName")->fetchColumn();
            echo "<div class='info'>📊 Row count: $count</div>";
            
            // Show sample data for small tables
            if ($count > 0 && $count <= 10) {
                echo "<h4>Sample Data:</h4>";
                $sampleData = $db->query("SELECT * FROM $tableName LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
                if (!empty($sampleData)) {
                    echo "<table>";
                    // Headers
                    echo "<tr>";
                    foreach (array_keys($sampleData[0]) as $header) {
                        echo "<th>" . htmlspecialchars($header) . "</th>";
                    }
                    echo "</tr>";
                    // Data
                    foreach ($sampleData as $row) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            $displayValue = $value;
                            if (strlen($displayValue) > 50) {
                                $displayValue = substr($displayValue, 0, 50) . '...';
                            }
                            echo "<td>" . htmlspecialchars($displayValue) . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error counting rows: " . $e->getMessage() . "</div>";
        }
    }
    
    // Test specific queries that were failing
    echo "<h2>🧪 Query Tests</h2>";
    
    if (in_array('Property', $tables)) {
        // Test COUNT query
        try {
            $result = $db->query('SELECT COUNT(*) FROM Property')->fetchColumn();
            echo "<div class='success'>✅ COUNT(*) FROM Property: $result</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ COUNT(*) failed: " . $e->getMessage() . "</div>";
        }
        
        // Test approval_status column
        try {
            $result = $db->query('SELECT approval_status FROM Property LIMIT 1')->fetch();
            echo "<div class='success'>✅ approval_status column exists</div>";
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ approval_status column missing: " . $e->getMessage() . "</div>";
            
            // Try to add it
            try {
                $db->exec("ALTER TABLE Property ADD COLUMN approval_status TEXT DEFAULT 'APPROVED'");
                echo "<div class='success'>✅ Added approval_status column</div>";
            } catch (Exception $e2) {
                echo "<div class='error'>❌ Failed to add approval_status: " . $e2->getMessage() . "</div>";
            }
        }
    }
    
    if (in_array('User', $tables)) {
        try {
            $result = $db->query('SELECT COUNT(*) FROM User')->fetchColumn();
            echo "<div class='success'>✅ COUNT(*) FROM User: $result</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ User COUNT(*) failed: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<h2>🎯 Recommendations</h2>";
    echo "<div class='info'>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Database connection is working</li>";
    echo "<li>✅ Tables are accessible</li>";
    echo "<li>🔧 Any missing columns have been added</li>";
    echo "<li>🚀 Admin dashboard should now work properly</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database Error: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Check if the database file exists and is readable.</div>";
}

echo "<h2>🔗 Quick Links</h2>";
echo "<a href='admin-dashboard.php' class='btn' style='background:#28a745;'>🏠 Admin Dashboard</a>";
echo "<a href='admin-login-direct.php' class='btn' style='background:#17a2b8;'>🔐 Admin Login</a>";
echo "<a href='properties/' class='btn' style='background:#6f42c1;'>🏘️ Properties</a>";

echo "</div>";
echo "</body></html>";
?>
