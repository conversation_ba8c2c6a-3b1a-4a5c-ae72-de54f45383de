<?php
/**
 * Quick Admin Reset Script for Production
 * Run this once to fix admin login issues
 * IMPORTANT: Delete this file after use!
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>🔧 Admin Reset Script</h2>";
    
    // Check if we're using SQLite or MySQL
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;
    
    // Admin credentials
    $admin_email = '<EMAIL>';
    $admin_password = 'Admin@2024!';
    $admin_hash = password_hash($admin_password, PASSWORD_DEFAULT);
    
    $backup_email = '<EMAIL>';
    $backup_password = 'admin123';
    $backup_hash = password_hash($backup_password, PASSWORD_DEFAULT);
    
    if ($isSQLite) {
        // SQLite - Check if admin exists
        $check_query = "SELECT id FROM User WHERE email = :email";
        $insert_query = "INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (:id, :name, :email, :password, 'ADMIN', 1, datetime('now'), datetime('now'))";
        $update_query = "UPDATE User SET password = :password, updatedAt = datetime('now') WHERE email = :email";
    } else {
        // MySQL - Check if admin exists
        $check_query = "SELECT id FROM users WHERE email = :email";
        $insert_query = "INSERT INTO users (id, name, email, password, role, is_active, created_at, updated_at) VALUES (:id, :name, :email, :password, 'ADMIN', 1, NOW(), NOW())";
        $update_query = "UPDATE users SET password = :password, updated_at = NOW() WHERE email = :email";
    }
    
    // Process main admin
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':email', $admin_email);
    $check_stmt->execute();
    $existing_admin = $check_stmt->fetch();
    
    if ($existing_admin) {
        // Update existing admin
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':password', $admin_hash);
        $update_stmt->bindParam(':email', $admin_email);
        $update_stmt->execute();
        echo "<p>✅ Updated existing admin: $admin_email</p>";
    } else {
        // Create new admin
        $insert_stmt = $db->prepare($insert_query);
        $admin_id = 'admin_' . uniqid();
        $insert_stmt->bindParam(':id', $admin_id);
        $insert_stmt->bindParam(':name', 'Housing Admin');
        $insert_stmt->bindParam(':email', $admin_email);
        $insert_stmt->bindParam(':password', $admin_hash);
        $insert_stmt->execute();
        echo "<p>✅ Created new admin: $admin_email</p>";
    }
    
    // Process backup admin
    $check_stmt->bindParam(':email', $backup_email);
    $check_stmt->execute();
    $existing_backup = $check_stmt->fetch();
    
    if ($existing_backup) {
        // Update existing backup admin
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':password', $backup_hash);
        $update_stmt->bindParam(':email', $backup_email);
        $update_stmt->execute();
        echo "<p>✅ Updated backup admin: $backup_email</p>";
    } else {
        // Create backup admin
        $insert_stmt = $db->prepare($insert_query);
        $backup_id = 'admin_backup_' . uniqid();
        $insert_stmt->bindParam(':id', $backup_id);
        $insert_stmt->bindParam(':name', 'Admin User');
        $insert_stmt->bindParam(':email', $backup_email);
        $insert_stmt->bindParam(':password', $backup_hash);
        $insert_stmt->execute();
        echo "<p>✅ Created backup admin: $backup_email</p>";
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Admin Reset Complete!</h3>";
    echo "<p><strong>Primary Admin:</strong><br>";
    echo "Email: $admin_email<br>";
    echo "Password: $admin_password</p>";
    echo "<p><strong>Backup Admin:</strong><br>";
    echo "Email: $backup_email<br>";
    echo "Password: $backup_password</p>";
    echo "<p><strong>Admin Login URL:</strong><br>";
    echo "<a href='/admin/login' target='_blank'>https://housing.okayy.in/admin/login</a></p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>⚠️ Security Notice</h4>";
    echo "<p>Please delete this file (reset-admin.php) immediately after use for security!</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h4>❌ Error</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
