-- Admin User Setup for housing.okayy.in
-- Run this after importing database.sql

-- Insert admin user
INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `phone`, `is_active`, `created_at`, `updated_at`) VALUES
('admin_housing_okayy', 'Housing Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ADMIN', '+91-9876543210', 1, NOW(), NOW());

-- Note: The password hash above is for 'Admin@2024!'
-- If you need to change the password, use this PHP code to generate a new hash:
-- echo password_hash('your_new_password', PASSWORD_DEFAULT);

-- Insert a test regular user (optional)
INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `phone`, `is_active`, `created_at`, `updated_at`) VALUES
('user_test_housing', 'Test User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'USER', '+91-9876543211', 1, NOW(), NOW());

-- Note: The password hash above is for 'user123'

-- Verify admin user was created
SELECT id, name, email, role, is_active, created_at FROM users WHERE role = 'ADMIN';
