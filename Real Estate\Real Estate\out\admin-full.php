<?php
// Full Admin System - Complete Property and User Management
session_start();
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: admin-login-direct.php');
    exit;
}

// Database connection
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Connection failed: ' . $e->getMessage());
}

// Handle POST actions for properties
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $propertyId = $_POST['property_id'] ?? '';
    switch ($_POST['action']) {
        case 'approve':
            $stmt = $db->prepare("UPDATE Property SET approvalStatus = 'APPROVED', isApproved = 1, approvedAt = CURRENT_TIMESTAMP, approvedBy = :adminId WHERE id = :id");
            $stmt->execute(['id' => $propertyId, 'adminId' => $_SESSION['admin_user']['id']]);
            break;
        case 'reject':
            $reason = $_POST['rejection_reason'] ?? '';
            $stmt = $db->prepare("UPDATE Property SET approvalStatus = 'REJECTED', rejectionReason = :reason WHERE id = :id");
            $stmt->execute(['id' => $propertyId, 'reason' => $reason]);
            break;
        case 'delete':
            $stmt = $db->prepare("DELETE FROM Property WHERE id = :id");
            $stmt->execute(['id' => $propertyId]);
            break;
        case 'delete_user':
            $userId = $_POST['user_id'] ?? '';
            if ($userId !== $_SESSION['admin_user']['id']) {
                $stmt = $db->prepare("DELETE FROM User WHERE id = :id");
                $stmt->execute(['id' => $userId]);
            }
            header('Location: admin-full.php?tab=users');
            exit;
    }
    header('Location: admin-full.php?tab=properties');
    exit;
}

// Fetch counts for overview
try {
    $propertyCount = $db->query('SELECT COUNT(*) FROM Property')->fetchColumn();
    $userCount = $db->query('SELECT COUNT(*) FROM User')->fetchColumn();
    $approvedCount = $db->query("SELECT COUNT(*) FROM Property WHERE approvalStatus = 'APPROVED'")->fetchColumn();
    $pendingCount = $db->query("SELECT COUNT(*) FROM Property WHERE approvalStatus = 'PENDING'")->fetchColumn();
} catch (Exception $e) {
    $propertyCount = $userCount = $approvedCount = $pendingCount = 0;
}

// Fetch data for tabs
$tab = $_GET['tab'] ?? 'overview';
if ($tab === 'properties') {
    try {
        $pendingProperties = $db->query("SELECT p.*, u.name as owner_name, u.email as owner_email FROM Property p JOIN User u ON p.ownerId = u.id WHERE approvalStatus = 'PENDING'")->fetchAll(PDO::FETCH_ASSOC);
        $allProperties = $db->query("SELECT p.*, u.name as owner_name, u.email as owner_email FROM Property p JOIN User u ON p.ownerId = u.id ORDER BY p.createdAt DESC")->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $pendingProperties = $allProperties = [];
    }
} elseif ($tab === 'users') {
    try {
        $users = $db->query("SELECT u.*, (SELECT COUNT(*) FROM Property WHERE ownerId = u.id) as properties_count FROM User u ORDER BY u.createdAt DESC")->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $users = [];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Full Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="admin-header text-white p-4">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">🏠 Real Estate Admin System</h1>
            <div class="flex items-center space-x-4">
                <span>Welcome, <?= htmlspecialchars($_SESSION['admin_user']['name']) ?></span>
                <a href="dashboard-admin.php?logout=1" class="bg-red-600 px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </div>

    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-gray-800 text-white p-4">
            <h2 class="text-xl font-semibold mb-4">Admin Navigation</h2>
            <nav class="space-y-1">
                <a href="?tab=overview" class="block px-3 py-2 rounded-md hover:bg-gray-700 <?= !isset($_GET['tab']) || $_GET['tab'] === 'overview' ? 'bg-gray-700' : '' ?>">📊 Dashboard Overview</a>
                <a href="?tab=properties" class="block px-3 py-2 rounded-md hover:bg-gray-700 <?= $_GET['tab'] === 'properties' ? 'bg-gray-700' : '' ?>">🏠 Properties Management</a>
                <a href="?tab=users" class="block px-3 py-2 rounded-md hover:bg-gray-700 <?= $_GET['tab'] === 'users' ? 'bg-gray-700' : '' ?>">👥 User Management</a>
                
                <div class="border-t border-gray-600 mt-4 pt-4">
                    <a href="dashboard-admin.php" class="block px-3 py-2 rounded-md hover:bg-blue-600 text-blue-300 hover:text-white">🔙 Simple Dashboard</a>
                    <a href="database-diagnostic.php" class="block px-3 py-2 rounded-md hover:bg-green-600 text-green-300 hover:text-white">🔍 Database Tools</a>
                    <a href="dashboard-admin.php?logout=1" class="block px-3 py-2 rounded-md hover:bg-red-600 text-red-300 hover:text-white">🚪 Logout</a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <?php if (!isset($_GET['tab']) || $_GET['tab'] === 'overview'): ?>
                <h1 class="text-3xl font-bold text-gray-800 mb-6">Admin Dashboard Overview</h1>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-gray-700 mb-2">Total Properties</h2>
                        <p class="text-3xl font-bold text-indigo-600"><?php echo $propertyCount; ?></p>
                    </div>
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-gray-700 mb-2">Approved Properties</h2>
                        <p class="text-3xl font-bold text-green-600"><?php echo $approvedCount; ?></p>
                    </div>
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-lg font-semibent text-gray-700 mb-2">Pending Approval</h2>
                        <p class="text-3xl font-bold text-yellow-600"><?php echo $pendingCount; ?></p>
                    </div>
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-gray-700 mb-2">Total Users</h2>
                        <p class="text-3xl font-bold text-purple-600"><?php echo $userCount; ?></p>
                    </div>
                </div>

                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">Quick Actions</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <a href="?tab=properties" class="bg-blue-600 text-white p-4 rounded-lg text-center hover:bg-blue-700">
                            🏠 Manage Properties
                        </a>
                        <a href="?tab=users" class="bg-green-600 text-white p-4 rounded-lg text-center hover:bg-green-700">
                            👥 Manage Users
                        </a>
                        <a href="database-diagnostic.php" class="bg-purple-600 text-white p-4 rounded-lg text-center hover:bg-purple-700">
                            🔍 System Diagnostic
                        </a>
                    </div>
                </div>

            <?php elseif ($tab === 'properties'): ?>
                <h1 class="text-3xl font-bold text-gray-800 mb-6">Properties Management</h1>
                
                <?php if (!empty($pendingProperties)): ?>
                <div class="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">⏳ Pending Approvals (<?= count($pendingProperties) ?>)</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Title</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Price</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Owner</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pendingProperties as $prop): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="py-3 px-4 border-b">
                                            <div class="font-medium"><?php echo htmlspecialchars($prop['title']); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($prop['type'] ?? ''); ?></div>
                                        </td>
                                        <td class="py-3 px-4 border-b">₹<?php echo number_format($prop['price']); ?></td>
                                        <td class="py-3 px-4 border-b">
                                            <div><?php echo htmlspecialchars($prop['owner_name'] ?? 'Unknown'); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($prop['owner_email']); ?></div>
                                        </td>
                                        <td class="py-3 px-4 border-b">
                                            <div class="flex space-x-2">
                                                <form method="POST" class="inline">
                                                    <input type="hidden" name="action" value="approve">
                                                    <input type="hidden" name="property_id" value="<?php echo $prop['id']; ?>">
                                                    <button type="submit" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">✅ Approve</button>
                                                </form>
                                                <form method="POST" class="inline">
                                                    <input type="hidden" name="action" value="reject">
                                                    <input type="hidden" name="property_id" value="<?php echo $prop['id']; ?>">
                                                    <input type="text" name="rejection_reason" placeholder="Reason" class="border p-1 text-sm w-20" required>
                                                    <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">❌ Reject</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>

                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">🏠 All Properties (<?= count($allProperties) ?>)</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Property</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Price</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Status</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Owner</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($allProperties as $prop): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="py-3 px-4 border-b">
                                            <div class="font-medium"><?php echo htmlspecialchars($prop['title']); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($prop['type'] ?? '') . ' • ' . htmlspecialchars($prop['listingType'] ?? ''); ?></div>
                                        </td>
                                        <td class="py-3 px-4 border-b">₹<?php echo number_format($prop['price']); ?></td>
                                        <td class="py-3 px-4 border-b">
                                            <span class="px-2 py-1 text-xs rounded-full <?php 
                                                echo $prop['approvalStatus'] === 'APPROVED' ? 'bg-green-100 text-green-800' : 
                                                    ($prop['approvalStatus'] === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); 
                                            ?>">
                                                <?php echo htmlspecialchars($prop['approvalStatus']); ?>
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 border-b">
                                            <div><?php echo htmlspecialchars($prop['owner_name'] ?? 'Unknown'); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($prop['owner_email']); ?></div>
                                        </td>
                                        <td class="py-3 px-4 border-b">
                                            <form method="POST" class="inline">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="property_id" value="<?php echo $prop['id']; ?>">
                                                <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700" onclick="return confirm('Are you sure you want to delete this property?')">🗑️ Delete</button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

            <?php elseif ($tab === 'users'): ?>
                <h1 class="text-3xl font-bold text-gray-800 mb-6">User Management</h1>
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">👥 All Users (<?= count($users) ?>)</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="py-3 px-4 border-b text-left font-semibold">User</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Email</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Role</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Properties</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Status</th>
                                    <th class="py-3 px-4 border-b text-left font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="py-3 px-4 border-b">
                                            <div class="font-medium"><?php echo htmlspecialchars($user['name'] ?? 'No Name'); ?></div>
                                            <div class="text-sm text-gray-500">ID: <?php echo htmlspecialchars($user['id']); ?></div>
                                        </td>
                                        <td class="py-3 px-4 border-b"><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td class="py-3 px-4 border-b">
                                            <span class="px-2 py-1 text-xs rounded-full <?php
                                                echo $user['role'] === 'ADMIN' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800';
                                            ?>">
                                                <?php echo htmlspecialchars($user['role']); ?>
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 border-b">
                                            <span class="font-semibold"><?php echo $user['properties_count']; ?></span> properties
                                        </td>
                                        <td class="py-3 px-4 border-b">
                                            <span class="px-2 py-1 text-xs rounded-full <?php
                                                echo $user['isActive'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                                            ?>">
                                                <?php echo $user['isActive'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 border-b">
                                            <?php if ($user['role'] !== 'ADMIN' && $user['id'] !== $_SESSION['admin_user']['id']): ?>
                                                <form method="POST" class="inline">
                                                    <input type="hidden" name="action" value="delete_user">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700" onclick="return confirm('Are you sure you want to delete this user and all their properties?')">🗑️ Delete</button>
                                                </form>
                                            <?php else: ?>
                                                <span class="text-gray-400 text-sm">Protected</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

            <?php endif; ?>
        </main>
    </div>

    <script>
        // Auto-refresh pending count every 30 seconds
        setTimeout(function() {
            if (window.location.search.includes('tab=properties')) {
                window.location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
