<?php
// Fix Admin Login Script
// This creates a simplified login that works with our current database

echo "<h1>🔧 Fix Admin Login</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

try {
    // Connect to database
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Create UserSession table if it doesn't exist
    $db->exec("
        CREATE TABLE IF NOT EXISTS UserSession (
            id TEXT PRIMARY KEY,
            userId TEXT NOT NULL,
            sessionToken TEXT NOT NULL,
            expiresAt TEXT NOT NULL,
            createdAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (userId) REFERENCES User(id)
        )
    ");
    echo "<p class='success'>✅ Created UserSession table</p>";
    
    // Verify admin user exists
    $stmt = $db->prepare('SELECT id, email, password FROM User WHERE role = ? LIMIT 1');
    $stmt->execute(['ADMIN']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p class='success'>✅ Admin user found: " . $admin['email'] . "</p>";
        
        // Test password verification
        $testPassword = 'Admin@2024!';
        if (password_verify($testPassword, $admin['password'])) {
            echo "<p class='success'>✅ Admin password verification works</p>";
        } else {
            echo "<p class='error'>❌ Admin password verification failed</p>";
            
            // Reset admin password
            $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
            $stmt = $db->prepare('UPDATE User SET password = ? WHERE id = ?');
            $stmt->execute([$newHash, $admin['id']]);
            echo "<p class='success'>✅ Admin password reset to: Admin@2024!</p>";
        }
    } else {
        echo "<p class='error'>❌ No admin user found</p>";
        
        // Create admin user
        $hashedPassword = password_hash('Admin@2024!', PASSWORD_DEFAULT);
        $adminId = 'admin_' . uniqid();
        $now = date('Y-m-d H:i:s');
        
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $stmt->execute([
            $adminId,
            'Admin User',
            '<EMAIL>',
            $hashedPassword,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        
        echo "<p class='success'>✅ Created new admin user</p>";
    }
    
    echo "<h2>✅ Admin Login Fixed!</h2>";
    echo "<p class='success'>Admin login should now work properly.</p>";
    echo "<p><strong>Admin Credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "<li><strong>Password:</strong> Admin@2024!</li>";
    echo "</ul>";
    
    echo "<p><strong>Test Login:</strong></p>";
    echo "<p><a href='admin/login/' target='_blank' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Go to Admin Login</a></p>";
    
    echo "<p><strong>Other Tests:</strong></p>";
    echo "<p><a href='simple-test.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Run Simple Test</a></p>";
    echo "<p><a href='properties/' target='_blank' style='background:#17a2b8;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Check Properties Page</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><small>Generated on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
