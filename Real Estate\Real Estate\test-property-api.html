<!DOCTYPE html>
<html>
<head>
    <title>Test Property API</title>
</head>
<body>
    <h1>Test Property API</h1>
    <button onclick="testProperties()">Test Properties List</button>
    <button onclick="testPropertyDetail()">Test Property Detail</button>
    <div id="result"></div>

    <script>
        async function testProperties() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing properties list...';

            try {
                const response = await fetch('http://localhost:8000/php-backend/api/properties/index.php', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                console.log('Properties response status:', response.status);
                const data = await response.json();
                console.log('Properties response data:', data);

                resultDiv.innerHTML = `
                    <h3>Properties List Response:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Data:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;

            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        async function testPropertyDetail() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing property detail...';

            try {
                // First get a property ID from the list
                const listResponse = await fetch('http://localhost:8000/php-backend/api/properties/index.php');
                const listData = await listResponse.json();
                
                if (listData.properties && listData.properties.length > 0) {
                    const propertyId = listData.properties[0].id;
                    
                    const response = await fetch(`http://localhost:8000/php-backend/api/properties/get.php?id=${propertyId}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        credentials: 'include'
                    });

                    console.log('Property detail response status:', response.status);
                    const data = await response.json();
                    console.log('Property detail response data:', data);

                    resultDiv.innerHTML = `
                        <h3>Property Detail Response:</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Property ID:</strong> ${propertyId}</p>
                        <p><strong>Data:</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>No properties found to test detail</h3>
                        <pre>${JSON.stringify(listData, null, 2)}</pre>
                    `;
                }

            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
