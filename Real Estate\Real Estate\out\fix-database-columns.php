<?php
// Fix Database Columns - Add missing approval_status column
echo "<h1>🔧 Fix Database Columns</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f8f9fa;} 
.container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;}
.btn{background:#007bff;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;text-decoration:none;display:inline-block;margin:5px;}
</style>";

echo "<div class='container'>";

try {
    // Connect to database
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Check current Property table structure
    echo "<h2>📋 Current Property Table Structure</h2>";
    $stmt = $db->query("PRAGMA table_info(Property)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasApprovalStatus = false;
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . htmlspecialchars($column['name']) . " (" . htmlspecialchars($column['type']) . ")</li>";
        if ($column['name'] === 'approval_status') {
            $hasApprovalStatus = true;
        }
    }
    echo "</ul>";
    
    // Add approval_status column if missing
    if (!$hasApprovalStatus) {
        echo "<h2>🔧 Adding Missing approval_status Column</h2>";
        $db->exec("ALTER TABLE Property ADD COLUMN approval_status TEXT DEFAULT 'APPROVED'");
        echo "<p class='success'>✅ Added approval_status column</p>";
        
        // Update all existing properties to APPROVED
        $stmt = $db->exec("UPDATE Property SET approval_status = 'APPROVED' WHERE approval_status IS NULL");
        echo "<p class='success'>✅ Updated existing properties to APPROVED status</p>";
    } else {
        echo "<p class='success'>✅ approval_status column already exists</p>";
    }
    
    // Check if we have any properties
    echo "<h2>📊 Property Statistics</h2>";
    $stmt = $db->query('SELECT COUNT(*) as total FROM Property');
    $totalProperties = $stmt->fetch()['total'];
    echo "<p><strong>Total Properties:</strong> $totalProperties</p>";
    
    if ($totalProperties > 0) {
        $stmt = $db->query('SELECT approval_status, COUNT(*) as count FROM Property GROUP BY approval_status');
        $statusCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>By Status:</strong></p>";
        echo "<ul>";
        foreach ($statusCounts as $status) {
            echo "<li>" . htmlspecialchars($status['approval_status']) . ": " . $status['count'] . "</li>";
        }
        echo "</ul>";
        
        // Show sample properties
        echo "<h3>Sample Properties:</h3>";
        $stmt = $db->query('SELECT title, price, type, approval_status FROM Property LIMIT 3');
        $sampleProperties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($sampleProperties as $prop) {
            echo "<div style='background:#f8f9fa;padding:10px;margin:5px 0;border-radius:5px;'>";
            echo "<strong>" . htmlspecialchars($prop['title']) . "</strong><br>";
            echo "Price: ₹" . number_format($prop['price']) . " | Type: " . htmlspecialchars($prop['type']) . "<br>";
            echo "Status: " . htmlspecialchars($prop['approval_status']) . "<br>";
            echo "</div>";
        }
    }
    
    echo "<h2>✅ Database Fixed!</h2>";
    echo "<p class='success'>The admin dashboard should now work without errors.</p>";
    
    echo "<h2>🎯 Test Now</h2>";
    echo "<a href='admin-dashboard.php' class='btn' style='background:#28a745;'>🏠 Go to Admin Dashboard</a>";
    echo "<a href='properties/' class='btn' style='background:#17a2b8;'>🔍 View Properties Page</a>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "</div>";
echo "<hr>";
echo "<p><small>Generated: " . date('Y-m-d H:i:s') . "</small></p>";
?>
