<?php
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "=== Checking Property Images ===\n";
    
    $stmt = $db->prepare('SELECT id, title, images FROM Property WHERE approvalStatus = "APPROVED" LIMIT 3');
    $stmt->execute();
    $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($properties as $property) {
        echo "\nProperty: {$property['title']} (ID: {$property['id']})\n";
        echo "Raw images data: " . substr($property['images'], 0, 200) . "...\n";
        
        if ($property['images']) {
            $images = json_decode($property['images'], true);
            if ($images && is_array($images)) {
                echo "Parsed images (" . count($images) . "):\n";
                foreach ($images as $i => $image) {
                    echo "  $i: $image\n";
                    
                    // Check if image file exists
                    $imagePath = "php-backend/uploads/" . basename($image);
                    if (file_exists($imagePath)) {
                        echo "    ✅ File exists: $imagePath\n";
                    } else {
                        echo "    ❌ File missing: $imagePath\n";
                    }
                }
            } else {
                echo "❌ Failed to parse images JSON\n";
            }
        } else {
            echo "❌ No images data\n";
        }
    }

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
