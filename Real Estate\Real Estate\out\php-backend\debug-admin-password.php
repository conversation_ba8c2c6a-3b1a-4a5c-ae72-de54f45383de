<?php
/**
 * Debug script to check admin password and create correct hash
 * IMPORTANT: Delete this file after use!
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>🔍 Admin Password Debug</h2>";
    
    // Check if we're using SQLite or MySQL
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;
    
    if ($isSQLite) {
        echo "<p>Using SQLite database</p>";
        $query = "SELECT id, name, email, password, role FROM User WHERE email = '<EMAIL>' OR email = '<EMAIL>'";
    } else {
        echo "<p>Using MySQL database</p>";
        $query = "SELECT id, name, email, password, role FROM users WHERE email = '<EMAIL>' OR email = '<EMAIL>'";
    }
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    echo "<h3>📋 Current Admin Users:</h3>";
    foreach ($admins as $admin) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<strong>ID:</strong> " . htmlspecialchars($admin['id']) . "<br>";
        echo "<strong>Name:</strong> " . htmlspecialchars($admin['name']) . "<br>";
        echo "<strong>Email:</strong> " . htmlspecialchars($admin['email']) . "<br>";
        echo "<strong>Role:</strong> " . htmlspecialchars($admin['role']) . "<br>";
        echo "<strong>Password Hash:</strong> " . htmlspecialchars(substr($admin['password'], 0, 50)) . "...<br>";
        echo "</div>";
    }
    
    // Test password verification
    echo "<h3>🔐 Password Verification Test:</h3>";
    
    $test_passwords = [
        'admin123',
        'Admin@2024!',
        'new1234',
        'admin@123'
    ];
    
    foreach ($admins as $admin) {
        echo "<h4>Testing for: " . htmlspecialchars($admin['email']) . "</h4>";
        foreach ($test_passwords as $test_password) {
            $is_valid = password_verify($test_password, $admin['password']);
            $status = $is_valid ? "✅ VALID" : "❌ Invalid";
            echo "<p>Password '$test_password': $status</p>";
        }
    }
    
    // Generate new password hashes
    echo "<h3>🔧 Generate New Password Hashes:</h3>";
    $new_passwords = [
        'Admin@2024!' => password_hash('Admin@2024!', PASSWORD_DEFAULT),
        'admin123' => password_hash('admin123', PASSWORD_DEFAULT),
    ];
    
    foreach ($new_passwords as $password => $hash) {
        echo "<p><strong>Password:</strong> $password<br>";
        echo "<strong>Hash:</strong> $hash</p>";
    }
    
    // Update admin password if needed
    if (isset($_GET['update_password'])) {
        $new_password = 'Admin@2024!';
        $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        if ($isSQLite) {
            $update_query = "UPDATE User SET password = :password WHERE email = '<EMAIL>' OR email = '<EMAIL>'";
        } else {
            $update_query = "UPDATE users SET password = :password WHERE email = '<EMAIL>' OR email = '<EMAIL>'";
        }
        
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':password', $new_hash);
        
        if ($update_stmt->execute()) {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "✅ Admin password updated successfully!<br>";
            echo "New password: <strong>Admin@2024!</strong>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ Failed to update password";
            echo "</div>";
        }
    }
    
    echo "<h3>🔧 Actions:</h3>";
    echo "<a href='?update_password=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Update Admin Password</a>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ Error: " . htmlspecialchars($e->getMessage());
    echo "</div>";
}
?>
