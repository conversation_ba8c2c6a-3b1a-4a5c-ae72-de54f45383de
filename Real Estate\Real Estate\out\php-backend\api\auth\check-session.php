<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get session token from cookie or header
    $session_token = $_COOKIE['session_token'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    
    if (!$session_token) {
        sendResponse(['user' => null]);
    }
    
    // Remove 'Bearer ' prefix if present
    if (strpos($session_token, 'Bearer ') === 0) {
        $session_token = substr($session_token, 7);
    }
    
    // Check if we're using SQLite or MySQL
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;
    
    if ($isSQLite) {
        // SQLite database (development)
        $query = "SELECT us.*, u.id, u.name, u.email, u.role, u.isActive as is_active 
                  FROM UserSession us 
                  JOIN User u ON us.userId = u.id 
                  WHERE us.sessionToken = :token AND us.expiresAt > datetime('now')";
        $cleanup_query = "DELETE FROM UserSession WHERE sessionToken = :token";
    } else {
        // MySQL database (production)
        $query = "SELECT us.*, u.id, u.name, u.email, u.role, u.is_active 
                  FROM user_sessions us 
                  JOIN users u ON us.user_id = u.id 
                  WHERE us.session_token = :token AND us.expires_at > NOW()";
        $cleanup_query = "DELETE FROM user_sessions WHERE session_token = :token";
    }

    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();

    $session = $stmt->fetch();

    if (!$session) {
        // Clean up expired session
        $cleanup_stmt = $db->prepare($cleanup_query);
        $cleanup_stmt->bindParam(':token', $session_token);
        $cleanup_stmt->execute();

        sendResponse(['user' => null]);
    }
    
    if (!$session['is_active']) {
        sendResponse(['user' => null]);
    }
    
    // Return user data
    $user = [
        'id' => $session['id'],
        'name' => $session['name'],
        'email' => $session['email'],
        'role' => $session['role']
    ];
    
    sendResponse(['user' => $user]);
    
} catch (Exception $e) {
    error_log("Session check error: " . $e->getMessage());
    sendResponse(['user' => null]);
}
?>
