<?php
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "=== Marking Properties as Featured ===\n";
    
    // Mark first 3 properties as featured
    $updateStmt = $db->prepare('UPDATE Property SET isFeatured = 1 WHERE id IN (SELECT id FROM Property LIMIT 3)');
    $updateStmt->execute();
    
    echo "Marked first 3 properties as featured\n";
    
    // Verify
    $stmt = $db->prepare('SELECT id, title, isFeatured FROM Property WHERE isFeatured = 1');
    $stmt->execute();
    $featured = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nFeatured properties:\n";
    foreach ($featured as $prop) {
        echo "- {$prop['title']} (ID: {$prop['id']})\n";
    }

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
