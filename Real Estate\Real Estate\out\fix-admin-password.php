<?php
// Fix Admin Password - Reset and verify admin credentials
echo "<h1>🔧 Fix Admin Password</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f8f9fa;} 
.container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;}
.btn{background:#007bff;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;text-decoration:none;display:inline-block;margin:5px;}
.btn-success{background:#28a745;} .btn-danger{background:#dc3545;}
pre{background:#f1f1f1;padding:10px;border-radius:5px;overflow-x:auto;}
</style>";

echo "<div class='container'>";

try {
    // Connect to database
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Check current admin user
    echo "<h2>📋 Current Admin Status</h2>";
    $stmt = $db->prepare('SELECT id, name, email, password, role, isActive FROM User WHERE email = ?');
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p class='success'>✅ Admin user found</p>";
        echo "<p><strong>ID:</strong> " . htmlspecialchars($admin['id']) . "</p>";
        echo "<p><strong>Name:</strong> " . htmlspecialchars($admin['name']) . "</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($admin['email']) . "</p>";
        echo "<p><strong>Role:</strong> " . htmlspecialchars($admin['role']) . "</p>";
        echo "<p><strong>Active:</strong> " . ($admin['isActive'] ? 'Yes' : 'No') . "</p>";
        
        // Test current password
        echo "<h2>🔐 Password Test</h2>";
        $testPassword = 'Admin@2024!';
        if (password_verify($testPassword, $admin['password'])) {
            echo "<p class='success'>✅ Current password works with: $testPassword</p>";
        } else {
            echo "<p class='error'>❌ Current password doesn't work with: $testPassword</p>";
            echo "<p class='info'>🔧 Resetting password...</p>";
            
            // Reset password
            $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
            $stmt = $db->prepare('UPDATE User SET password = ?, isActive = 1 WHERE id = ?');
            $stmt->execute([$newHash, $admin['id']]);
            
            echo "<p class='success'>✅ Password reset successfully!</p>";
            
            // Verify the reset worked
            $stmt = $db->prepare('SELECT password FROM User WHERE id = ?');
            $stmt->execute([$admin['id']]);
            $newAdmin = $stmt->fetch();
            
            if (password_verify($testPassword, $newAdmin['password'])) {
                echo "<p class='success'>✅ Password reset verified!</p>";
            } else {
                echo "<p class='error'>❌ Password reset failed!</p>";
            }
        }
        
        // Ensure user is active
        if (!$admin['isActive']) {
            echo "<p class='info'>🔧 Activating admin user...</p>";
            $stmt = $db->prepare('UPDATE User SET isActive = 1 WHERE id = ?');
            $stmt->execute([$admin['id']]);
            echo "<p class='success'>✅ Admin user activated!</p>";
        }
        
    } else {
        echo "<p class='error'>❌ Admin user not found</p>";
        echo "<p class='info'>🔧 Creating new admin user...</p>";
        
        // Create new admin user
        $hashedPassword = password_hash('Admin@2024!', PASSWORD_DEFAULT);
        $adminId = 'admin_' . uniqid();
        $now = date('Y-m-d H:i:s');
        
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $stmt->execute([
            $adminId,
            'Admin User',
            '<EMAIL>',
            $hashedPassword,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        
        echo "<p class='success'>✅ New admin user created!</p>";
    }
    
    // Test login API directly
    echo "<h2>🧪 Test Login API</h2>";
    
    $loginData = json_encode([
        'email' => '<EMAIL>',
        'password' => 'Admin@2024!'
    ]);
    
    // Use file_get_contents for simple POST
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => $loginData
        ]
    ]);
    
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/php-backend/api/auth/login.php';
    $response = @file_get_contents($apiUrl, false, $context);
    
    if ($response) {
        echo "<p class='success'>✅ API responded</p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        $data = json_decode($response, true);
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p class='success'>✅ Login API working perfectly!</p>";
        } else {
            echo "<p class='error'>❌ Login API returned error: " . ($data['error'] ?? 'Unknown') . "</p>";
        }
    } else {
        echo "<p class='error'>❌ Could not reach API</p>";
        
        // Check if API file exists
        if (file_exists('php-backend/api/auth/login.php')) {
            echo "<p class='info'>ℹ️ API file exists, might be a server configuration issue</p>";
        } else {
            echo "<p class='error'>❌ API file missing</p>";
        }
    }
    
    echo "<h2>✅ Admin Login Fixed!</h2>";
    echo "<div style='background:#d4edda;padding:15px;border-radius:5px;border-left:4px solid #28a745;'>";
    echo "<p><strong>✅ Admin credentials are now ready:</strong></p>";
    echo "<p><strong>Email:</strong> <EMAIL></p>";
    echo "<p><strong>Password:</strong> Admin@2024!</p>";
    echo "</div>";
    
    echo "<h2>🎯 Test Now</h2>";
    echo "<a href='admin-login-working.html' class='btn btn-success'>🔐 Try Admin Login</a>";
    echo "<a href='properties/' class='btn' style='background:#17a2b8;'>🏠 Check Properties</a>";
    echo "<a href='debug-login.php' class='btn' style='background:#ffc107;color:#000;'>🔍 Debug Login</a>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "</div>";
echo "<hr>";
echo "<p><small>Generated: " . date('Y-m-d H:i:s') . "</small></p>";
?>
