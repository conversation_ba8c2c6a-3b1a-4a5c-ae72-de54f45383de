<?php
echo "=== Testing Admin Login API (Simple) ===\n";

// Simulate POST request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_HOST'] = 'localhost:8000';

// Simulate JSON input
$jsonInput = json_encode([
    'email' => '<EMAIL>',
    'password' => 'Admin@2024!'
]);

// Mock php://input
$tempFile = tempnam(sys_get_temp_dir(), 'test_input');
file_put_contents($tempFile, $jsonInput);

// Override file_get_contents for php://input
function file_get_contents_override($filename) {
    global $tempFile;
    if ($filename === 'php://input') {
        return file_get_contents($tempFile);
    }
    return file_get_contents($filename);
}

// Start output buffering to capture the API response
ob_start();

try {
    // Include the login API
    include 'php-backend/api/auth/login.php';
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();
echo "API Response: $output\n";

// Clean up
unlink($tempFile);
?>
