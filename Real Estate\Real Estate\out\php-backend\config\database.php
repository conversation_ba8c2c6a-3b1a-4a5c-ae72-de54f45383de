<?php
// Database configuration for hybrid deployment
class Database {
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            // Use SQLite for development, MySQL for production
            if ($_SERVER['HTTP_HOST'] === 'localhost:3000' || $_SERVER['HTTP_HOST'] === 'localhost:8000' || php_sapi_name() === 'cli-server') {
                // Development: Use SQLite
                $this->conn = new PDO('sqlite:' . __DIR__ . '/../../prisma/dev.db');
                $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            } else {
                // Production: Use MySQL
                $host = 'localhost';
                $db_name = 'u357173570_housingokayy';
                $username = 'u357173570_housingokayy';
                $password = 'kingking999@K'; // Change this to your actual password

                $this->conn = new PDO(
                    "mysql:host=" . $host . ";dbname=" . $db_name,
                    $username,
                    $password,
                    array(
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                    )
                );
            }
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed: " . $exception->getMessage());
        }

        return $this->conn;
    }
}

// Helper functions
function setCorsHeaders() {
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization");
    header("Content-Type: application/json; charset=UTF-8");
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit();
}

function sendError($message, $status = 400) {
    http_response_code($status);
    echo json_encode(['error' => $message, 'success' => false]);
    exit();
}

function validateInput($input, $required_fields) {
    if (!$input) {
        sendError('Invalid JSON input', 400);
    }
    
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            sendError("Missing required field: $field", 422);
        }
    }
    
    return $input;
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}
?>
