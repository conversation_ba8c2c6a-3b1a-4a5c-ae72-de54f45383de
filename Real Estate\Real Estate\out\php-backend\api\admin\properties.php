<?php
require_once '../../config/database.php';

setCorsHeaders();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check admin authentication
    $session_token = $_COOKIE['session_token'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    
    if (!$session_token) {
        sendError('Authentication required', 401);
    }
    
    // Remove 'Bearer ' prefix if present
    if (strpos($session_token, 'Bearer ') === 0) {
        $session_token = substr($session_token, 7);
    }
    
    // Check if we're using SQLite or MySQL
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;
    
    // Verify admin session
    if ($isSQLite) {
        $auth_query = "SELECT u.role FROM UserSession us 
                       JOIN User u ON us.userId = u.id 
                       WHERE us.sessionToken = :token AND us.expiresAt > datetime('now')";
    } else {
        $auth_query = "SELECT u.role FROM user_sessions us 
                       JOIN users u ON us.user_id = u.id 
                       WHERE us.session_token = :token AND us.expires_at > NOW()";
    }
    
    $auth_stmt = $db->prepare($auth_query);
    $auth_stmt->bindParam(':token', $session_token);
    $auth_stmt->execute();
    $auth_result = $auth_stmt->fetch();
    
    if (!$auth_result || $auth_result['role'] !== 'ADMIN') {
        sendError('Admin access required', 403);
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get all properties for admin
        if ($isSQLite) {
            $query = "SELECT p.*, u.name as owner_name, u.email as owner_email 
                      FROM Property p 
                      LEFT JOIN User u ON p.ownerId = u.id";
        } else {
            $query = "SELECT p.*, u.name as owner_name, u.email as owner_email 
                      FROM properties p 
                      LEFT JOIN users u ON p.owner_id = u.id";
        }
        
        // Add status filter if provided
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            if ($isSQLite) {
                $query .= " WHERE p.approvalStatus = :status";
            } else {
                $query .= " WHERE p.approval_status = :status";
            }
        }
        
        // Add ordering
        if ($isSQLite) {
            $query .= " ORDER BY p.createdAt DESC";
        } else {
            $query .= " ORDER BY p.created_at DESC";
        }
        
        $stmt = $db->prepare($query);
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $stmt->bindParam(':status', $_GET['status']);
        }
        $stmt->execute();
        
        $properties = $stmt->fetchAll();
        
        sendResponse([
            'success' => true,
            'properties' => $properties
        ]);
        
    } else {
        sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Admin properties API error: " . $e->getMessage());
    sendError('Failed to fetch admin properties', 500);
}
?>
