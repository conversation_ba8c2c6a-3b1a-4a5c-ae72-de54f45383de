<?php
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "=== Checking Admin Users ===\n";
    $stmt = $db->prepare('SELECT id, name, email, role FROM User WHERE role = "ADMIN"');
    $stmt->execute();
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($admins)) {
        echo "No admin users found!\n";
        
        // Create an admin user
        echo "Creating admin user...\n";
        $hashedPassword = password_hash('Admin@2024!', PASSWORD_DEFAULT);
        
        $insertStmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $adminId = 'admin_' . uniqid();
        $now = date('Y-m-d H:i:s');
        
        $insertStmt->execute([
            $adminId,
            'Admin User',
            '<EMAIL>',
            $hashedPassword,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        
        echo "Admin user created successfully!\n";
        echo "Email: <EMAIL>\n";
        echo "Password: Admin@2024!\n";
    } else {
        echo "Found " . count($admins) . " admin user(s):\n";
        foreach ($admins as $admin) {
            echo "- ID: {$admin['id']}, Name: {$admin['name']}, Email: {$admin['email']}\n";
        }
    }

    echo "\n=== Checking All Users ===\n";
    $allStmt = $db->prepare('SELECT id, name, email, role, isActive FROM User LIMIT 10');
    $allStmt->execute();
    $allUsers = $allStmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($allUsers as $user) {
        echo "- {$user['email']} ({$user['role']}) - Active: " . ($user['isActive'] ? 'Yes' : 'No') . "\n";
    }

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
