<!DOCTYPE html>
<html>
<head>
    <title>Test Admin Login API</title>
</head>
<body>
    <h1>Test Admin Login API</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';

            try {
                console.log('Testing login API...');
                
                const response = await fetch('http://localhost:8000/php-backend/api/auth/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin@2024!'
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                const data = await response.json();
                console.log('Response data:', data);

                resultDiv.innerHTML = `
                    <h3>Response:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Data:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;

            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
