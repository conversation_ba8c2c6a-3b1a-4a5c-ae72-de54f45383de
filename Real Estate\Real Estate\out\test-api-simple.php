<?php
// Simple API Test
echo "<h1>🔌 Simple API Test</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";

echo "<h2>1. Direct Database Query</h2>";
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->query('SELECT id, title, price, type, listingType, approval_status FROM Property LIMIT 5');
    $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p class='success'>✅ Found " . count($properties) . " properties in database</p>";
    
    if (count($properties) > 0) {
        echo "<h3>Sample Properties:</h3>";
        foreach ($properties as $prop) {
            echo "<div style='border:1px solid #ddd;padding:10px;margin:5px 0;border-radius:5px;'>";
            echo "<strong>" . htmlspecialchars($prop['title']) . "</strong><br>";
            echo "Price: ₹" . number_format($prop['price']) . "<br>";
            echo "Type: " . $prop['type'] . " | Listing: " . $prop['listingType'] . "<br>";
            echo "Status: " . $prop['approval_status'] . "<br>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>2. Test Search API File</h2>";
if (file_exists('php-backend/api/properties/search.php')) {
    echo "<p class='success'>✅ Search API file exists</p>";
    
    // Try to get the API response
    $apiUrl = 'php-backend/api/properties/search.php?limit=3';
    echo "<p><strong>Testing:</strong> <a href='$apiUrl' target='_blank'>$apiUrl</a></p>";
    
    // Test with curl if available
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p>HTTP Code: $httpCode</p>";
        if ($response) {
            $data = json_decode($response, true);
            if ($data) {
                echo "<p class='success'>✅ API returns valid JSON</p>";
                echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
            } else {
                echo "<p class='error'>❌ API returns invalid JSON</p>";
                echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
            }
        } else {
            echo "<p class='error'>❌ No response from API</p>";
        }
    } else {
        echo "<p class='info'>ℹ️ cURL not available for testing</p>";
    }
    
} else {
    echo "<p class='error'>❌ Search API file not found</p>";
}

echo "<h2>3. Frontend Test</h2>";
echo "<p>Test these pages manually:</p>";
echo "<ul>";
echo "<li><a href='properties/' target='_blank'>Properties Page</a> - Should show properties</li>";
echo "<li><a href='admin/login/' target='_blank'>Admin Login</a> - Should load login form</li>";
echo "<li><a href='php-backend/api/properties/search.php?limit=5' target='_blank'>API Direct</a> - Should return JSON</li>";
echo "</ul>";

echo "<h2>4. Quick Fixes</h2>";
echo "<p>If something isn't working:</p>";
echo "<ul>";
echo "<li><a href='fix-admin-login.php' style='background:#dc3545;color:white;padding:5px 10px;text-decoration:none;border-radius:3px;'>Fix Admin Login</a></li>";
echo "<li><a href='simple-test.php' style='background:#28a745;color:white;padding:5px 10px;text-decoration:none;border-radius:3px;'>Run Simple Test</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Generated: " . date('Y-m-d H:i:s') . "</small></p>";
?>
