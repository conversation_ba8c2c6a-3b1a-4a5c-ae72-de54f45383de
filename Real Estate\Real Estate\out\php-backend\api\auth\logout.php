<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get session token from cookie or header
    $session_token = $_COOKIE['session_token'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    
    if ($session_token) {
        // Remove 'Bearer ' prefix if present
        if (strpos($session_token, 'Bearer ') === 0) {
            $session_token = substr($session_token, 7);
        }
        
        // Check if we're using SQLite or MySQL
        $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;
        
        // Delete session from database
        if ($isSQLite) {
            $delete_query = "DELETE FROM UserSession WHERE sessionToken = :token";
        } else {
            $delete_query = "DELETE FROM user_sessions WHERE session_token = :token";
        }
        
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->bindParam(':token', $session_token);
        $delete_stmt->execute();
        
        // Clear session cookie
        setcookie('session_token', '', time() - 3600, '/', '', false, true);
    }
    
    sendResponse([
        'success' => true,
        'message' => 'Logged out successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Logout error: " . $e->getMessage());
    sendResponse([
        'success' => true,
        'message' => 'Logged out successfully'
    ]); // Always return success for logout
}
?>
