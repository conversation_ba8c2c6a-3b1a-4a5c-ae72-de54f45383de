[{"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx": "1", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\login\\page.tsx": "2", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\properties\\page.tsx": "3", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\users\\page.tsx": "4", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\blog\\page.tsx": "5", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\blog\\[slug]\\page.tsx": "6", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\buy\\page.tsx": "7", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\contact\\page.tsx": "8", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\dashboard\\page.tsx": "9", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\login\\page.tsx": "11", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\okayy\\page.tsx": "12", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\page.tsx": "13", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\page.tsx": "14", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\[id]\\page.tsx": "15", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\create\\page.tsx": "16", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\page.tsx": "17", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\[id]\\page.tsx": "18", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\rent\\page.tsx": "19", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\sell\\page.tsx": "20", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\signup\\page.tsx": "21", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\CtaSection.tsx": "22", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\ErrorBoundary.tsx": "23", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\FeaturedProperties.tsx": "24", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\Footer.tsx": "25", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\Navbar.tsx": "26", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\NotificationBell.tsx": "27", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PGFilters.tsx": "28", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyAnalytics.tsx": "29", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyApprovalTable.tsx": "30", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyCard.tsx": "31", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyCategories.tsx": "32", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyEditForm.tsx": "33", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyFilters.tsx": "34", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyListingForm.tsx": "35", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\SearchBar.tsx": "36", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\SessionProvider.tsx": "37", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\Testimonial.tsx": "38", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\TestimonialsSection.tsx": "39", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\config\\api.ts": "40", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\hooks\\useTestimonials.ts": "41", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\lib\\auth.ts": "42", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\lib\\db.ts": "43", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\lib\\prisma.ts": "44", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\lib\\utils.ts": "45", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\types\\next-auth.d.ts": "46", "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\utils\\searchUtils.ts": "47"}, {"size": 2879, "mtime": 1752644654497, "results": "48", "hashOfConfig": "49"}, {"size": 5604, "mtime": 1752666390643, "results": "50", "hashOfConfig": "49"}, {"size": 12229, "mtime": 1752645075613, "results": "51", "hashOfConfig": "49"}, {"size": 12052, "mtime": 1752644566618, "results": "52", "hashOfConfig": "49"}, {"size": 8883, "mtime": 1752593987156, "results": "53", "hashOfConfig": "49"}, {"size": 6405, "mtime": 1751023945961, "results": "54", "hashOfConfig": "49"}, {"size": 14448, "mtime": 1752646003417, "results": "55", "hashOfConfig": "49"}, {"size": 16323, "mtime": 1752076475765, "results": "56", "hashOfConfig": "49"}, {"size": 13375, "mtime": 1752643145691, "results": "57", "hashOfConfig": "49"}, {"size": 2533, "mtime": 1752604426048, "results": "58", "hashOfConfig": "49"}, {"size": 4920, "mtime": 1752592903851, "results": "59", "hashOfConfig": "49"}, {"size": 13352, "mtime": 1751110408978, "results": "60", "hashOfConfig": "49"}, {"size": 17059, "mtime": 1752069438205, "results": "61", "hashOfConfig": "49"}, {"size": 9431, "mtime": 1752646022865, "results": "62", "hashOfConfig": "49"}, {"size": 12347, "mtime": 1752908549966, "results": "63", "hashOfConfig": "49"}, {"size": 4279, "mtime": 1752599073281, "results": "64", "hashOfConfig": "49"}, {"size": 14093, "mtime": 1752909601153, "results": "65", "hashOfConfig": "49"}, {"size": 13161, "mtime": 1752908523089, "results": "66", "hashOfConfig": "49"}, {"size": 16820, "mtime": 1752645981843, "results": "67", "hashOfConfig": "49"}, {"size": 32514, "mtime": 1751038010060, "results": "68", "hashOfConfig": "49"}, {"size": 6951, "mtime": 1752588808137, "results": "69", "hashOfConfig": "49"}, {"size": 1411, "mtime": 1750407836870, "results": "70", "hashOfConfig": "49"}, {"size": 3718, "mtime": 1752604506899, "results": "71", "hashOfConfig": "49"}, {"size": 6613, "mtime": 1752903240748, "results": "72", "hashOfConfig": "49"}, {"size": 11541, "mtime": 1752076464493, "results": "73", "hashOfConfig": "49"}, {"size": 11277, "mtime": 1752599626747, "results": "74", "hashOfConfig": "49"}, {"size": 6877, "mtime": 1752599586948, "results": "75", "hashOfConfig": "49"}, {"size": 10477, "mtime": 1751038346893, "results": "76", "hashOfConfig": "49"}, {"size": 9573, "mtime": 1750422855393, "results": "77", "hashOfConfig": "49"}, {"size": 3685, "mtime": 1752589292100, "results": "78", "hashOfConfig": "49"}, {"size": 9645, "mtime": 1752645922831, "results": "79", "hashOfConfig": "49"}, {"size": 2067, "mtime": 1750407763074, "results": "80", "hashOfConfig": "49"}, {"size": 10476, "mtime": 1751034237067, "results": "81", "hashOfConfig": "49"}, {"size": 24377, "mtime": 1752582620604, "results": "82", "hashOfConfig": "49"}, {"size": 22569, "mtime": 1752607229648, "results": "83", "hashOfConfig": "49"}, {"size": 21978, "mtime": 1752209972772, "results": "84", "hashOfConfig": "49"}, {"size": 244, "mtime": 1750419048728, "results": "85", "hashOfConfig": "49"}, {"size": 1868, "mtime": 1750408517380, "results": "86", "hashOfConfig": "49"}, {"size": 4782, "mtime": 1750408572972, "results": "87", "hashOfConfig": "49"}, {"size": 6484, "mtime": 1752908563697, "results": "88", "hashOfConfig": "49"}, {"size": 1075, "mtime": 1750408471567, "results": "89", "hashOfConfig": "49"}, {"size": 1571, "mtime": 1751019620908, "results": "90", "hashOfConfig": "49"}, {"size": 102, "mtime": 1752123892659, "results": "91", "hashOfConfig": "49"}, {"size": 56, "mtime": 1752407785645, "results": "92", "hashOfConfig": "49"}, {"size": 3361, "mtime": 1750419734452, "results": "93", "hashOfConfig": "49"}, {"size": 392, "mtime": 1750418920284, "results": "94", "hashOfConfig": "49"}, {"size": 12689, "mtime": 1751019711415, "results": "95", "hashOfConfig": "49"}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "14s00di", {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx", ["237", "238"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\login\\page.tsx", ["239"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\properties\\page.tsx", ["240", "241", "242"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\users\\page.tsx", ["243", "244", "245"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\blog\\page.tsx", ["246", "247", "248"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\blog\\[slug]\\page.tsx", ["249"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\buy\\page.tsx", ["250", "251", "252", "253", "254"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\contact\\page.tsx", ["255", "256"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\dashboard\\page.tsx", ["257", "258", "259", "260"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\login\\page.tsx", ["261"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\okayy\\page.tsx", ["262"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\page.tsx", ["263", "264", "265"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\page.tsx", ["266", "267", "268"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\[id]\\page.tsx", ["269"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\create\\page.tsx", ["270"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\page.tsx", ["271", "272", "273", "274", "275", "276", "277", "278", "279", "280"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\[id]\\page.tsx", ["281", "282", "283", "284", "285"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\rent\\page.tsx", ["286", "287", "288", "289", "290", "291", "292"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\sell\\page.tsx", ["293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\signup\\page.tsx", ["305"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\CtaSection.tsx", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\FeaturedProperties.tsx", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\Footer.tsx", ["306"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\Navbar.tsx", ["307"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\NotificationBell.tsx", ["308"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PGFilters.tsx", ["309", "310"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyAnalytics.tsx", ["311", "312"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyApprovalTable.tsx", ["313", "314"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyCard.tsx", ["315", "316"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyCategories.tsx", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyEditForm.tsx", ["317", "318", "319", "320"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyFilters.tsx", ["321"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\PropertyListingForm.tsx", ["322", "323", "324", "325", "326"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\SearchBar.tsx", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\SessionProvider.tsx", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\Testimonial.tsx", ["327", "328"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\components\\TestimonialsSection.tsx", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\config\\api.ts", ["329", "330", "331", "332"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\hooks\\useTestimonials.ts", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\lib\\db.ts", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\lib\\utils.ts", ["333"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\types\\next-auth.d.ts", ["334"], [], "C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\utils\\searchUtils.ts", [], [], {"ruleId": "335", "severity": 2, "message": "336", "line": 8, "column": 36, "nodeType": "337", "messageId": "338", "endLine": 8, "endColumn": 39, "suggestions": "339"}, {"ruleId": "340", "severity": 1, "message": "341", "line": 33, "column": 6, "nodeType": "342", "endLine": 33, "endColumn": 8, "suggestions": "343"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 41, "column": 21, "nodeType": "337", "messageId": "338", "endLine": 41, "endColumn": 24, "suggestions": "344"}, {"ruleId": "340", "severity": 1, "message": "341", "line": 51, "column": 6, "nodeType": "342", "endLine": 51, "endColumn": 8, "suggestions": "345"}, {"ruleId": "346", "severity": 2, "message": "347", "line": 102, "column": 14, "nodeType": null, "messageId": "348", "endLine": 102, "endColumn": 19}, {"ruleId": "346", "severity": 2, "message": "347", "line": 128, "column": 14, "nodeType": null, "messageId": "348", "endLine": 128, "endColumn": 19}, {"ruleId": "340", "severity": 1, "message": "341", "line": 46, "column": 6, "nodeType": "342", "endLine": 46, "endColumn": 8, "suggestions": "349"}, {"ruleId": "346", "severity": 2, "message": "347", "line": 86, "column": 14, "nodeType": null, "messageId": "348", "endLine": 86, "endColumn": 19}, {"ruleId": "346", "severity": 2, "message": "347", "line": 113, "column": 14, "nodeType": null, "messageId": "348", "endLine": 113, "endColumn": 19}, {"ruleId": "335", "severity": 2, "message": "336", "line": 41, "column": 46, "nodeType": "337", "messageId": "338", "endLine": 41, "endColumn": 49, "suggestions": "350"}, {"ruleId": "346", "severity": 2, "message": "351", "line": 50, "column": 16, "nodeType": null, "messageId": "348", "endLine": 50, "endColumn": 24}, {"ruleId": "346", "severity": 2, "message": "352", "line": 70, "column": 9, "nodeType": null, "messageId": "348", "endLine": 70, "endColumn": 24}, {"ruleId": "346", "severity": 2, "message": "353", "line": 4, "column": 8, "nodeType": null, "messageId": "348", "endLine": 4, "endColumn": 13}, {"ruleId": "340", "severity": 1, "message": "354", "line": 67, "column": 6, "nodeType": "342", "endLine": 67, "endColumn": 8, "suggestions": "355"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 69, "column": 55, "nodeType": "337", "messageId": "338", "endLine": 69, "endColumn": 58, "suggestions": "356"}, {"ruleId": "340", "severity": 1, "message": "357", "line": 72, "column": 6, "nodeType": "342", "endLine": 72, "endColumn": 8, "suggestions": "358"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 106, "column": 108, "nodeType": "361", "messageId": "362", "suggestions": "363"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 183, "column": 46, "nodeType": "337", "messageId": "338", "endLine": 183, "endColumn": 49, "suggestions": "364"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 209, "column": 43, "nodeType": "361", "messageId": "362", "suggestions": "365"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 245, "column": 24, "nodeType": "361", "messageId": "362", "suggestions": "366"}, {"ruleId": "346", "severity": 2, "message": "367", "line": 59, "column": 11, "nodeType": null, "messageId": "348", "endLine": 59, "endColumn": 23}, {"ruleId": "335", "severity": 2, "message": "336", "line": 74, "column": 50, "nodeType": "337", "messageId": "338", "endLine": 74, "endColumn": 53, "suggestions": "368"}, {"ruleId": "340", "severity": 1, "message": "341", "line": 97, "column": 6, "nodeType": "342", "endLine": 97, "endColumn": 8, "suggestions": "369"}, {"ruleId": "370", "severity": 1, "message": "371", "line": 282, "column": 19, "nodeType": "372", "endLine": 289, "endColumn": 21}, {"ruleId": "335", "severity": 2, "message": "336", "line": 47, "column": 21, "nodeType": "337", "messageId": "338", "endLine": 47, "endColumn": 24, "suggestions": "373"}, {"ruleId": "346", "severity": 2, "message": "353", "line": 5, "column": 8, "nodeType": null, "messageId": "348", "endLine": 5, "endColumn": 13}, {"ruleId": "359", "severity": 2, "message": "360", "line": 121, "column": 83, "nodeType": "361", "messageId": "362", "suggestions": "374"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 205, "column": 37, "nodeType": "361", "messageId": "362", "suggestions": "375"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 231, "column": 55, "nodeType": "361", "messageId": "362", "suggestions": "376"}, {"ruleId": "346", "severity": 2, "message": "377", "line": 39, "column": 10, "nodeType": null, "messageId": "348", "endLine": 39, "endColumn": 20}, {"ruleId": "340", "severity": 1, "message": "378", "line": 107, "column": 6, "nodeType": "342", "endLine": 107, "endColumn": 20, "suggestions": "379"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 109, "column": 43, "nodeType": "337", "messageId": "338", "endLine": 109, "endColumn": 46, "suggestions": "380"}, {"ruleId": "346", "severity": 2, "message": "347", "line": 60, "column": 12, "nodeType": null, "messageId": "348", "endLine": 60, "endColumn": 17}, {"ruleId": "335", "severity": 2, "message": "336", "line": 12, "column": 50, "nodeType": "337", "messageId": "338", "endLine": 12, "endColumn": 53, "suggestions": "381"}, {"ruleId": "346", "severity": 2, "message": "382", "line": 9, "column": 10, "nodeType": null, "messageId": "348", "endLine": 9, "endColumn": 23}, {"ruleId": "346", "severity": 2, "message": "383", "line": 47, "column": 10, "nodeType": null, "messageId": "348", "endLine": 47, "endColumn": 15}, {"ruleId": "340", "severity": 1, "message": "354", "line": 99, "column": 6, "nodeType": "342", "endLine": 99, "endColumn": 8, "suggestions": "384"}, {"ruleId": "340", "severity": 1, "message": "385", "line": 107, "column": 6, "nodeType": "342", "endLine": 107, "endColumn": 15, "suggestions": "386"}, {"ruleId": "340", "severity": 1, "message": "387", "line": 114, "column": 6, "nodeType": "342", "endLine": 114, "endColumn": 23, "suggestions": "388"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 143, "column": 50, "nodeType": "337", "messageId": "338", "endLine": 143, "endColumn": 53, "suggestions": "389"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 206, "column": 21, "nodeType": "337", "messageId": "338", "endLine": 206, "endColumn": 24, "suggestions": "390"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 229, "column": 55, "nodeType": "337", "messageId": "338", "endLine": 229, "endColumn": 58, "suggestions": "391"}, {"ruleId": "340", "severity": 1, "message": "357", "line": 240, "column": 6, "nodeType": "342", "endLine": 240, "endColumn": 8, "suggestions": "392"}, {"ruleId": "346", "severity": 2, "message": "393", "line": 250, "column": 9, "nodeType": null, "messageId": "348", "endLine": 250, "endColumn": 23}, {"ruleId": "346", "severity": 2, "message": "353", "line": 1, "column": 8, "nodeType": null, "messageId": "348", "endLine": 1, "endColumn": 13}, {"ruleId": "346", "severity": 2, "message": "347", "line": 50, "column": 12, "nodeType": null, "messageId": "348", "endLine": 50, "endColumn": 17}, {"ruleId": "370", "severity": 1, "message": "371", "line": 133, "column": 15, "nodeType": "372", "endLine": 140, "endColumn": 17}, {"ruleId": "370", "severity": 1, "message": "371", "line": 145, "column": 19, "nodeType": "372", "endLine": 152, "endColumn": 21}, {"ruleId": "370", "severity": 1, "message": "371", "line": 158, "column": 19, "nodeType": "372", "endLine": 162, "endColumn": 21}, {"ruleId": "346", "severity": 2, "message": "394", "line": 7, "column": 10, "nodeType": null, "messageId": "348", "endLine": 7, "endColumn": 22}, {"ruleId": "346", "severity": 2, "message": "395", "line": 20, "column": 10, "nodeType": null, "messageId": "348", "endLine": 20, "endColumn": 20}, {"ruleId": "346", "severity": 2, "message": "396", "line": 21, "column": 10, "nodeType": null, "messageId": "348", "endLine": 21, "endColumn": 17}, {"ruleId": "346", "severity": 2, "message": "377", "line": 22, "column": 10, "nodeType": null, "messageId": "348", "endLine": 22, "endColumn": 20}, {"ruleId": "340", "severity": 1, "message": "354", "line": 66, "column": 6, "nodeType": "342", "endLine": 66, "endColumn": 8, "suggestions": "397"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 68, "column": 55, "nodeType": "337", "messageId": "338", "endLine": 68, "endColumn": 58, "suggestions": "398"}, {"ruleId": "340", "severity": 1, "message": "357", "line": 71, "column": 6, "nodeType": "342", "endLine": 71, "endColumn": 8, "suggestions": "399"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 199, "column": 156, "nodeType": "361", "messageId": "362", "suggestions": "400"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 245, "column": 99, "nodeType": "361", "messageId": "362", "suggestions": "401"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 307, "column": 44, "nodeType": "361", "messageId": "362", "suggestions": "402"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 307, "column": 61, "nodeType": "361", "messageId": "362", "suggestions": "403"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 316, "column": 51, "nodeType": "361", "messageId": "362", "suggestions": "404"}, {"ruleId": "359", "severity": 2, "message": "405", "line": 517, "column": 17, "nodeType": "361", "messageId": "362", "suggestions": "406"}, {"ruleId": "359", "severity": 2, "message": "405", "line": 517, "column": 197, "nodeType": "361", "messageId": "362", "suggestions": "407"}, {"ruleId": "359", "severity": 2, "message": "405", "line": 545, "column": 17, "nodeType": "361", "messageId": "362", "suggestions": "408"}, {"ruleId": "359", "severity": 2, "message": "405", "line": 545, "column": 196, "nodeType": "361", "messageId": "362", "suggestions": "409"}, {"ruleId": "359", "severity": 2, "message": "405", "line": 573, "column": 17, "nodeType": "361", "messageId": "362", "suggestions": "410"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 573, "column": 186, "nodeType": "361", "messageId": "362", "suggestions": "411"}, {"ruleId": "359", "severity": 2, "message": "405", "line": 573, "column": 200, "nodeType": "361", "messageId": "362", "suggestions": "412"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 59, "column": 21, "nodeType": "337", "messageId": "338", "endLine": 59, "endColumn": 24, "suggestions": "413"}, {"ruleId": "359", "severity": 2, "message": "360", "line": 30, "column": 93, "nodeType": "361", "messageId": "362", "suggestions": "414"}, {"ruleId": "346", "severity": 2, "message": "353", "line": 5, "column": 8, "nodeType": null, "messageId": "348", "endLine": 5, "endColumn": 13}, {"ruleId": "335", "severity": 2, "message": "336", "line": 21, "column": 50, "nodeType": "337", "messageId": "338", "endLine": 21, "endColumn": 53, "suggestions": "415"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 7, "column": 29, "nodeType": "337", "messageId": "338", "endLine": 7, "endColumn": 32, "suggestions": "416"}, {"ruleId": "340", "severity": 1, "message": "417", "line": 55, "column": 6, "nodeType": "342", "endLine": 55, "endColumn": 15, "suggestions": "418"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 19, "column": 15, "nodeType": "337", "messageId": "338", "endLine": 19, "endColumn": 18, "suggestions": "419"}, {"ruleId": "340", "severity": 1, "message": "420", "line": 29, "column": 6, "nodeType": "342", "endLine": 29, "endColumn": 29, "suggestions": "421"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 29, "column": 21, "nodeType": "337", "messageId": "338", "endLine": 29, "endColumn": 24, "suggestions": "422"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 49, "column": 21, "nodeType": "337", "messageId": "338", "endLine": 49, "endColumn": 24, "suggestions": "423"}, {"ruleId": "346", "severity": 2, "message": "353", "line": 2, "column": 8, "nodeType": null, "messageId": "348", "endLine": 2, "endColumn": 13}, {"ruleId": "370", "severity": 1, "message": "371", "line": 78, "column": 9, "nodeType": "372", "endLine": 85, "endColumn": 11}, {"ruleId": "346", "severity": 2, "message": "424", "line": 43, "column": 7, "nodeType": null, "messageId": "348", "endLine": 43, "endColumn": 21}, {"ruleId": "346", "severity": 2, "message": "425", "line": 76, "column": 9, "nodeType": null, "messageId": "348", "endLine": 76, "endColumn": 28}, {"ruleId": "346", "severity": 2, "message": "426", "line": 85, "column": 9, "nodeType": null, "messageId": "348", "endLine": 85, "endColumn": 26}, {"ruleId": "346", "severity": 2, "message": "427", "line": 96, "column": 9, "nodeType": null, "messageId": "348", "endLine": 96, "endColumn": 20}, {"ruleId": "335", "severity": 2, "message": "336", "line": 6, "column": 29, "nodeType": "337", "messageId": "338", "endLine": 6, "endColumn": 32, "suggestions": "428"}, {"ruleId": "346", "severity": 2, "message": "429", "line": 7, "column": 11, "nodeType": null, "messageId": "348", "endLine": 7, "endColumn": 27}, {"ruleId": "335", "severity": 2, "message": "336", "line": 225, "column": 25, "nodeType": "337", "messageId": "338", "endLine": 225, "endColumn": 28, "suggestions": "430"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 258, "column": 21, "nodeType": "337", "messageId": "338", "endLine": 258, "endColumn": 24, "suggestions": "431"}, {"ruleId": "370", "severity": 1, "message": "371", "line": 575, "column": 17, "nodeType": "372", "endLine": 579, "endColumn": 19}, {"ruleId": "359", "severity": 2, "message": "360", "line": 636, "column": 56, "nodeType": "361", "messageId": "362", "suggestions": "432"}, {"ruleId": "359", "severity": 2, "message": "405", "line": 44, "column": 13, "nodeType": "361", "messageId": "362", "suggestions": "433"}, {"ruleId": "359", "severity": 2, "message": "405", "line": 44, "column": 21, "nodeType": "361", "messageId": "362", "suggestions": "434"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 117, "column": 49, "nodeType": "337", "messageId": "338", "endLine": 117, "endColumn": 52, "suggestions": "435"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 129, "column": 40, "nodeType": "337", "messageId": "338", "endLine": 129, "endColumn": 43, "suggestions": "436"}, {"ruleId": "335", "severity": 2, "message": "336", "line": 197, "column": 44, "nodeType": "337", "messageId": "338", "endLine": 197, "endColumn": 47, "suggestions": "437"}, {"ruleId": "438", "severity": 1, "message": "439", "line": 225, "column": 1, "nodeType": "440", "endLine": 236, "endColumn": 3}, {"ruleId": "335", "severity": 2, "message": "336", "line": 59, "column": 37, "nodeType": "337", "messageId": "338", "endLine": 59, "endColumn": 40, "suggestions": "441"}, {"ruleId": "346", "severity": 2, "message": "442", "line": 2, "column": 8, "nodeType": null, "messageId": "348", "endLine": 2, "endColumn": 16}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["443", "444"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'router'. Either include it or remove the dependency array.", "ArrayExpression", ["445"], ["446", "447"], ["448"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["449"], ["450", "451"], "'apiError' is defined but never used.", "'parseJsonSafely' is assigned a value but never used.", "'Image' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchProperties'. Either include it or remove the dependency array.", ["452"], ["453", "454"], "React Hook useCallback has a missing dependency: 'fetchProperties'. Either include it or remove the dependency array.", ["455"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["456", "457", "458", "459"], ["460", "461"], ["462", "463", "464", "465"], ["466", "467", "468", "469"], "'Notification' is defined but never used.", ["470", "471"], ["472"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["473", "474"], ["475", "476", "477", "478"], ["479", "480", "481", "482"], ["483", "484", "485", "486"], "'pagination' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPGProperties'. Either include it or remove the dependency array.", ["487"], ["488", "489"], ["490", "491"], "'propertiesAPI' is defined but never used.", "'error' is assigned a value but never used.", ["492"], "React Hook useEffect has missing dependencies: 'fetchProperties', 'pagination.limit', and 'pagination.page'. Either include them or remove the dependency array.", ["493"], "React Hook useEffect has missing dependencies: 'fetchProperties' and 'pagination.limit'. Either include them or remove the dependency array.", ["494"], ["495", "496"], ["497", "498"], ["499", "500"], ["501"], "'formatCurrency' is assigned a value but never used.", "'PropertyCard' is defined but never used.", "'properties' is assigned a value but never used.", "'loading' is assigned a value but never used.", ["502"], ["503", "504"], ["505"], ["506", "507", "508", "509"], ["510", "511", "512", "513"], ["514", "515", "516", "517"], ["518", "519", "520", "521"], ["522", "523", "524", "525"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["526", "527", "528", "529"], ["530", "531", "532", "533"], ["534", "535", "536", "537"], ["538", "539", "540", "541"], ["542", "543", "544", "545"], ["546", "547", "548", "549"], ["550", "551", "552", "553"], ["554", "555"], ["556", "557", "558", "559"], ["560", "561"], ["562", "563"], "React Hook useEffect has a missing dependency: 'onFilterChange'. Either include it or remove the dependency array. If 'onFilterChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["564"], ["565", "566"], "React Hook useEffect has a missing dependency: 'fetchAnalytics'. Either include it or remove the dependency array.", ["567"], ["568", "569"], ["570", "571"], "'AMENITIES_LIST' is assigned a value but never used.", "'handleAmenityToggle' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", "'removeImage' is assigned a value but never used.", ["572", "573"], "'PropertyFormData' is defined but never used.", ["574", "575"], ["576", "577"], ["578", "579", "580", "581"], ["582", "583", "584", "585"], ["586", "587", "588", "589"], ["590", "591"], ["592", "593"], ["594", "595"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", ["596", "597"], "'NextAuth' is defined but never used.", {"messageId": "598", "fix": "599", "desc": "600"}, {"messageId": "601", "fix": "602", "desc": "603"}, {"desc": "604", "fix": "605"}, {"messageId": "598", "fix": "606", "desc": "600"}, {"messageId": "601", "fix": "607", "desc": "603"}, {"desc": "604", "fix": "608"}, {"desc": "604", "fix": "609"}, {"messageId": "598", "fix": "610", "desc": "600"}, {"messageId": "601", "fix": "611", "desc": "603"}, {"desc": "612", "fix": "613"}, {"messageId": "598", "fix": "614", "desc": "600"}, {"messageId": "601", "fix": "615", "desc": "603"}, {"desc": "612", "fix": "616"}, {"messageId": "617", "data": "618", "fix": "619", "desc": "620"}, {"messageId": "617", "data": "621", "fix": "622", "desc": "623"}, {"messageId": "617", "data": "624", "fix": "625", "desc": "626"}, {"messageId": "617", "data": "627", "fix": "628", "desc": "629"}, {"messageId": "598", "fix": "630", "desc": "600"}, {"messageId": "601", "fix": "631", "desc": "603"}, {"messageId": "617", "data": "632", "fix": "633", "desc": "620"}, {"messageId": "617", "data": "634", "fix": "635", "desc": "623"}, {"messageId": "617", "data": "636", "fix": "637", "desc": "626"}, {"messageId": "617", "data": "638", "fix": "639", "desc": "629"}, {"messageId": "617", "data": "640", "fix": "641", "desc": "620"}, {"messageId": "617", "data": "642", "fix": "643", "desc": "623"}, {"messageId": "617", "data": "644", "fix": "645", "desc": "626"}, {"messageId": "617", "data": "646", "fix": "647", "desc": "629"}, {"messageId": "598", "fix": "648", "desc": "600"}, {"messageId": "601", "fix": "649", "desc": "603"}, {"desc": "604", "fix": "650"}, {"messageId": "598", "fix": "651", "desc": "600"}, {"messageId": "601", "fix": "652", "desc": "603"}, {"messageId": "617", "data": "653", "fix": "654", "desc": "620"}, {"messageId": "617", "data": "655", "fix": "656", "desc": "623"}, {"messageId": "617", "data": "657", "fix": "658", "desc": "626"}, {"messageId": "617", "data": "659", "fix": "660", "desc": "629"}, {"messageId": "617", "data": "661", "fix": "662", "desc": "620"}, {"messageId": "617", "data": "663", "fix": "664", "desc": "623"}, {"messageId": "617", "data": "665", "fix": "666", "desc": "626"}, {"messageId": "617", "data": "667", "fix": "668", "desc": "629"}, {"messageId": "617", "data": "669", "fix": "670", "desc": "620"}, {"messageId": "617", "data": "671", "fix": "672", "desc": "623"}, {"messageId": "617", "data": "673", "fix": "674", "desc": "626"}, {"messageId": "617", "data": "675", "fix": "676", "desc": "629"}, {"desc": "677", "fix": "678"}, {"messageId": "598", "fix": "679", "desc": "600"}, {"messageId": "601", "fix": "680", "desc": "603"}, {"messageId": "598", "fix": "681", "desc": "600"}, {"messageId": "601", "fix": "682", "desc": "603"}, {"desc": "612", "fix": "683"}, {"desc": "684", "fix": "685"}, {"desc": "686", "fix": "687"}, {"messageId": "598", "fix": "688", "desc": "600"}, {"messageId": "601", "fix": "689", "desc": "603"}, {"messageId": "598", "fix": "690", "desc": "600"}, {"messageId": "601", "fix": "691", "desc": "603"}, {"messageId": "598", "fix": "692", "desc": "600"}, {"messageId": "601", "fix": "693", "desc": "603"}, {"desc": "612", "fix": "694"}, {"desc": "612", "fix": "695"}, {"messageId": "598", "fix": "696", "desc": "600"}, {"messageId": "601", "fix": "697", "desc": "603"}, {"desc": "612", "fix": "698"}, {"messageId": "617", "data": "699", "fix": "700", "desc": "620"}, {"messageId": "617", "data": "701", "fix": "702", "desc": "623"}, {"messageId": "617", "data": "703", "fix": "704", "desc": "626"}, {"messageId": "617", "data": "705", "fix": "706", "desc": "629"}, {"messageId": "617", "data": "707", "fix": "708", "desc": "620"}, {"messageId": "617", "data": "709", "fix": "710", "desc": "623"}, {"messageId": "617", "data": "711", "fix": "712", "desc": "626"}, {"messageId": "617", "data": "713", "fix": "714", "desc": "629"}, {"messageId": "617", "data": "715", "fix": "716", "desc": "620"}, {"messageId": "617", "data": "717", "fix": "718", "desc": "623"}, {"messageId": "617", "data": "719", "fix": "720", "desc": "626"}, {"messageId": "617", "data": "721", "fix": "722", "desc": "629"}, {"messageId": "617", "data": "723", "fix": "724", "desc": "620"}, {"messageId": "617", "data": "725", "fix": "726", "desc": "623"}, {"messageId": "617", "data": "727", "fix": "728", "desc": "626"}, {"messageId": "617", "data": "729", "fix": "730", "desc": "629"}, {"messageId": "617", "data": "731", "fix": "732", "desc": "620"}, {"messageId": "617", "data": "733", "fix": "734", "desc": "623"}, {"messageId": "617", "data": "735", "fix": "736", "desc": "626"}, {"messageId": "617", "data": "737", "fix": "738", "desc": "629"}, {"messageId": "617", "data": "739", "fix": "740", "desc": "741"}, {"messageId": "617", "data": "742", "fix": "743", "desc": "744"}, {"messageId": "617", "data": "745", "fix": "746", "desc": "747"}, {"messageId": "617", "data": "748", "fix": "749", "desc": "750"}, {"messageId": "617", "data": "751", "fix": "752", "desc": "741"}, {"messageId": "617", "data": "753", "fix": "754", "desc": "744"}, {"messageId": "617", "data": "755", "fix": "756", "desc": "747"}, {"messageId": "617", "data": "757", "fix": "758", "desc": "750"}, {"messageId": "617", "data": "759", "fix": "760", "desc": "741"}, {"messageId": "617", "data": "761", "fix": "762", "desc": "744"}, {"messageId": "617", "data": "763", "fix": "764", "desc": "747"}, {"messageId": "617", "data": "765", "fix": "766", "desc": "750"}, {"messageId": "617", "data": "767", "fix": "768", "desc": "741"}, {"messageId": "617", "data": "769", "fix": "770", "desc": "744"}, {"messageId": "617", "data": "771", "fix": "772", "desc": "747"}, {"messageId": "617", "data": "773", "fix": "774", "desc": "750"}, {"messageId": "617", "data": "775", "fix": "776", "desc": "741"}, {"messageId": "617", "data": "777", "fix": "778", "desc": "744"}, {"messageId": "617", "data": "779", "fix": "780", "desc": "747"}, {"messageId": "617", "data": "781", "fix": "782", "desc": "750"}, {"messageId": "617", "data": "783", "fix": "784", "desc": "620"}, {"messageId": "617", "data": "785", "fix": "786", "desc": "623"}, {"messageId": "617", "data": "787", "fix": "788", "desc": "626"}, {"messageId": "617", "data": "789", "fix": "790", "desc": "629"}, {"messageId": "617", "data": "791", "fix": "792", "desc": "741"}, {"messageId": "617", "data": "793", "fix": "794", "desc": "744"}, {"messageId": "617", "data": "795", "fix": "796", "desc": "747"}, {"messageId": "617", "data": "797", "fix": "798", "desc": "750"}, {"messageId": "598", "fix": "799", "desc": "600"}, {"messageId": "601", "fix": "800", "desc": "603"}, {"messageId": "617", "data": "801", "fix": "802", "desc": "620"}, {"messageId": "617", "data": "803", "fix": "804", "desc": "623"}, {"messageId": "617", "data": "805", "fix": "806", "desc": "626"}, {"messageId": "617", "data": "807", "fix": "808", "desc": "629"}, {"messageId": "598", "fix": "809", "desc": "600"}, {"messageId": "601", "fix": "810", "desc": "603"}, {"messageId": "598", "fix": "811", "desc": "600"}, {"messageId": "601", "fix": "812", "desc": "603"}, {"desc": "813", "fix": "814"}, {"messageId": "598", "fix": "815", "desc": "600"}, {"messageId": "601", "fix": "816", "desc": "603"}, {"desc": "817", "fix": "818"}, {"messageId": "598", "fix": "819", "desc": "600"}, {"messageId": "601", "fix": "820", "desc": "603"}, {"messageId": "598", "fix": "821", "desc": "600"}, {"messageId": "601", "fix": "822", "desc": "603"}, {"messageId": "598", "fix": "823", "desc": "600"}, {"messageId": "601", "fix": "824", "desc": "603"}, {"messageId": "598", "fix": "825", "desc": "600"}, {"messageId": "601", "fix": "826", "desc": "603"}, {"messageId": "598", "fix": "827", "desc": "600"}, {"messageId": "601", "fix": "828", "desc": "603"}, {"messageId": "617", "data": "829", "fix": "830", "desc": "620"}, {"messageId": "617", "data": "831", "fix": "832", "desc": "623"}, {"messageId": "617", "data": "833", "fix": "834", "desc": "626"}, {"messageId": "617", "data": "835", "fix": "836", "desc": "629"}, {"messageId": "617", "data": "837", "fix": "838", "desc": "741"}, {"messageId": "617", "data": "839", "fix": "840", "desc": "744"}, {"messageId": "617", "data": "841", "fix": "842", "desc": "747"}, {"messageId": "617", "data": "843", "fix": "844", "desc": "750"}, {"messageId": "617", "data": "845", "fix": "846", "desc": "741"}, {"messageId": "617", "data": "847", "fix": "848", "desc": "744"}, {"messageId": "617", "data": "849", "fix": "850", "desc": "747"}, {"messageId": "617", "data": "851", "fix": "852", "desc": "750"}, {"messageId": "598", "fix": "853", "desc": "600"}, {"messageId": "601", "fix": "854", "desc": "603"}, {"messageId": "598", "fix": "855", "desc": "600"}, {"messageId": "601", "fix": "856", "desc": "603"}, {"messageId": "598", "fix": "857", "desc": "600"}, {"messageId": "601", "fix": "858", "desc": "603"}, {"messageId": "598", "fix": "859", "desc": "600"}, {"messageId": "601", "fix": "860", "desc": "603"}, "suggestUnknown", {"range": "861", "text": "862"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "863", "text": "864"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [router]", {"range": "865", "text": "866"}, {"range": "867", "text": "862"}, {"range": "868", "text": "864"}, {"range": "869", "text": "866"}, {"range": "870", "text": "866"}, {"range": "871", "text": "862"}, {"range": "872", "text": "864"}, "Update the dependencies array to be: [fetchProperties]", {"range": "873", "text": "874"}, {"range": "875", "text": "862"}, {"range": "876", "text": "864"}, {"range": "877", "text": "874"}, "replaceWithAlt", {"alt": "878"}, {"range": "879", "text": "880"}, "Replace with `&apos;`.", {"alt": "881"}, {"range": "882", "text": "883"}, "Replace with `&lsquo;`.", {"alt": "884"}, {"range": "885", "text": "886"}, "Replace with `&#39;`.", {"alt": "887"}, {"range": "888", "text": "889"}, "Replace with `&rsquo;`.", {"range": "890", "text": "862"}, {"range": "891", "text": "864"}, {"alt": "878"}, {"range": "892", "text": "893"}, {"alt": "881"}, {"range": "894", "text": "895"}, {"alt": "884"}, {"range": "896", "text": "897"}, {"alt": "887"}, {"range": "898", "text": "899"}, {"alt": "878"}, {"range": "900", "text": "901"}, {"alt": "881"}, {"range": "902", "text": "903"}, {"alt": "884"}, {"range": "904", "text": "905"}, {"alt": "887"}, {"range": "906", "text": "907"}, {"range": "908", "text": "862"}, {"range": "909", "text": "864"}, {"range": "910", "text": "866"}, {"range": "911", "text": "862"}, {"range": "912", "text": "864"}, {"alt": "878"}, {"range": "913", "text": "914"}, {"alt": "881"}, {"range": "915", "text": "916"}, {"alt": "884"}, {"range": "917", "text": "918"}, {"alt": "887"}, {"range": "919", "text": "920"}, {"alt": "878"}, {"range": "921", "text": "922"}, {"alt": "881"}, {"range": "923", "text": "924"}, {"alt": "884"}, {"range": "925", "text": "926"}, {"alt": "887"}, {"range": "927", "text": "928"}, {"alt": "878"}, {"range": "929", "text": "930"}, {"alt": "881"}, {"range": "931", "text": "932"}, {"alt": "884"}, {"range": "933", "text": "934"}, {"alt": "887"}, {"range": "935", "text": "936"}, "Update the dependencies array to be: [fetchPGProperties, searchParams]", {"range": "937", "text": "938"}, {"range": "939", "text": "862"}, {"range": "940", "text": "864"}, {"range": "941", "text": "862"}, {"range": "942", "text": "864"}, {"range": "943", "text": "874"}, "Update the dependencies array to be: [fetchProperties, filters, pagination.limit, pagination.page]", {"range": "944", "text": "945"}, "Update the dependencies array to be: [fetchProperties, pagination.limit, pagination.page]", {"range": "946", "text": "947"}, {"range": "948", "text": "862"}, {"range": "949", "text": "864"}, {"range": "950", "text": "862"}, {"range": "951", "text": "864"}, {"range": "952", "text": "862"}, {"range": "953", "text": "864"}, {"range": "954", "text": "874"}, {"range": "955", "text": "874"}, {"range": "956", "text": "862"}, {"range": "957", "text": "864"}, {"range": "958", "text": "874"}, {"alt": "878"}, {"range": "959", "text": "960"}, {"alt": "881"}, {"range": "961", "text": "962"}, {"alt": "884"}, {"range": "963", "text": "964"}, {"alt": "887"}, {"range": "965", "text": "966"}, {"alt": "878"}, {"range": "967", "text": "968"}, {"alt": "881"}, {"range": "969", "text": "970"}, {"alt": "884"}, {"range": "971", "text": "972"}, {"alt": "887"}, {"range": "973", "text": "974"}, {"alt": "878"}, {"range": "975", "text": "976"}, {"alt": "881"}, {"range": "977", "text": "978"}, {"alt": "884"}, {"range": "979", "text": "980"}, {"alt": "887"}, {"range": "981", "text": "982"}, {"alt": "878"}, {"range": "983", "text": "984"}, {"alt": "881"}, {"range": "985", "text": "986"}, {"alt": "884"}, {"range": "987", "text": "988"}, {"alt": "887"}, {"range": "989", "text": "990"}, {"alt": "878"}, {"range": "991", "text": "992"}, {"alt": "881"}, {"range": "993", "text": "994"}, {"alt": "884"}, {"range": "995", "text": "996"}, {"alt": "887"}, {"range": "997", "text": "998"}, {"alt": "999"}, {"range": "1000", "text": "1001"}, "Replace with `&quot;`.", {"alt": "1002"}, {"range": "1003", "text": "1004"}, "Replace with `&ldquo;`.", {"alt": "1005"}, {"range": "1006", "text": "1007"}, "Replace with `&#34;`.", {"alt": "1008"}, {"range": "1009", "text": "1010"}, "Replace with `&rdquo;`.", {"alt": "999"}, {"range": "1011", "text": "1012"}, {"alt": "1002"}, {"range": "1013", "text": "1014"}, {"alt": "1005"}, {"range": "1015", "text": "1016"}, {"alt": "1008"}, {"range": "1017", "text": "1018"}, {"alt": "999"}, {"range": "1019", "text": "1020"}, {"alt": "1002"}, {"range": "1021", "text": "1022"}, {"alt": "1005"}, {"range": "1023", "text": "1024"}, {"alt": "1008"}, {"range": "1025", "text": "1026"}, {"alt": "999"}, {"range": "1027", "text": "1028"}, {"alt": "1002"}, {"range": "1029", "text": "1030"}, {"alt": "1005"}, {"range": "1031", "text": "1032"}, {"alt": "1008"}, {"range": "1033", "text": "1034"}, {"alt": "999"}, {"range": "1035", "text": "1036"}, {"alt": "1002"}, {"range": "1037", "text": "1038"}, {"alt": "1005"}, {"range": "1039", "text": "1040"}, {"alt": "1008"}, {"range": "1041", "text": "1042"}, {"alt": "878"}, {"range": "1043", "text": "1044"}, {"alt": "881"}, {"range": "1045", "text": "1046"}, {"alt": "884"}, {"range": "1047", "text": "1048"}, {"alt": "887"}, {"range": "1049", "text": "1050"}, {"alt": "999"}, {"range": "1051", "text": "1052"}, {"alt": "1002"}, {"range": "1053", "text": "1054"}, {"alt": "1005"}, {"range": "1055", "text": "1056"}, {"alt": "1008"}, {"range": "1057", "text": "1058"}, {"range": "1059", "text": "862"}, {"range": "1060", "text": "864"}, {"alt": "878"}, {"range": "1061", "text": "1062"}, {"alt": "881"}, {"range": "1063", "text": "1064"}, {"alt": "884"}, {"range": "1065", "text": "1066"}, {"alt": "887"}, {"range": "1067", "text": "1068"}, {"range": "1069", "text": "862"}, {"range": "1070", "text": "864"}, {"range": "1071", "text": "862"}, {"range": "1072", "text": "864"}, "Update the dependencies array to be: [filters, onFilterChange]", {"range": "1073", "text": "1074"}, {"range": "1075", "text": "862"}, {"range": "1076", "text": "864"}, "Update the dependencies array to be: [fetchAnalytics, properties, timeRange]", {"range": "1077", "text": "1078"}, {"range": "1079", "text": "862"}, {"range": "1080", "text": "864"}, {"range": "1081", "text": "862"}, {"range": "1082", "text": "864"}, {"range": "1083", "text": "862"}, {"range": "1084", "text": "864"}, {"range": "1085", "text": "862"}, {"range": "1086", "text": "864"}, {"range": "1087", "text": "862"}, {"range": "1088", "text": "864"}, {"alt": "878"}, {"range": "1089", "text": "1090"}, {"alt": "881"}, {"range": "1091", "text": "1092"}, {"alt": "884"}, {"range": "1093", "text": "1094"}, {"alt": "887"}, {"range": "1095", "text": "1096"}, {"alt": "999"}, {"range": "1097", "text": "1098"}, {"alt": "1002"}, {"range": "1099", "text": "1100"}, {"alt": "1005"}, {"range": "1101", "text": "1102"}, {"alt": "1008"}, {"range": "1103", "text": "1104"}, {"alt": "999"}, {"range": "1105", "text": "1106"}, {"alt": "1002"}, {"range": "1107", "text": "1108"}, {"alt": "1005"}, {"range": "1109", "text": "1110"}, {"alt": "1008"}, {"range": "1111", "text": "1112"}, {"range": "1113", "text": "862"}, {"range": "1114", "text": "864"}, {"range": "1115", "text": "862"}, {"range": "1116", "text": "864"}, {"range": "1117", "text": "862"}, {"range": "1118", "text": "864"}, {"range": "1119", "text": "862"}, {"range": "1120", "text": "864"}, [228, 231], "unknown", [228, 231], "never", [849, 851], "[router]", [1265, 1268], [1265, 1268], [1242, 1244], [1131, 1133], [1030, 1033], [1030, 1033], [1872, 1874], "[fetchProperties]", [1932, 1935], [1932, 1935], [2050, 2052], "&apos;", [3576, 3711], "\n                Start by getting pre-approved for a mortgage to understand your budget and show sellers you&apos;re serious.\n              ", "&lsquo;", [3576, 3711], "\n                Start by getting pre-approved for a mortgage to understand your budget and show sellers you&lsquo;re serious.\n              ", "&#39;", [3576, 3711], "\n                Start by getting pre-approved for a mortgage to understand your budget and show sellers you&#39;re serious.\n              ", "&rsquo;", [3576, 3711], "\n                Start by getting pre-approved for a mortgage to understand your budget and show sellers you&rsquo;re serious.\n              ", [7852, 7855], [7852, 7855], [13106, 13363], "\n                To sell your property, you&apos;ll need proof of ownership, property tax records, mortgage information, home inspection reports, and any relevant permits for renovations. Our agents can guide you through the specific requirements.\n              ", [13106, 13363], "\n                To sell your property, you&lsquo;ll need proof of ownership, property tax records, mortgage information, home inspection reports, and any relevant permits for renovations. Our agents can guide you through the specific requirements.\n              ", [13106, 13363], "\n                To sell your property, you&#39;ll need proof of ownership, property tax records, mortgage information, home inspection reports, and any relevant permits for renovations. Our agents can guide you through the specific requirements.\n              ", [13106, 13363], "\n                To sell your property, you&rsquo;ll need proof of ownership, property tax records, mortgage information, home inspection reports, and any relevant permits for renovations. Our agents can guide you through the specific requirements.\n              ", [15805, 15924], "\n            Whether you&apos;re buying, selling, or renting, our team is here to help you every step of the way.\n          ", [15805, 15924], "\n            Whether you&lsquo;re buying, selling, or renting, our team is here to help you every step of the way.\n          ", [15805, 15924], "\n            Whether you&#39;re buying, selling, or renting, our team is here to help you every step of the way.\n          ", [15805, 15924], "\n            Whether you&rsquo;re buying, selling, or renting, our team is here to help you every step of the way.\n          ", [1351, 1354], [1351, 1354], [2248, 2250], [1367, 1370], [1367, 1370], [5623, 5734], "\n              Explore our diverse range of property types to find exactly what you&apos;re looking for\n            ", [5623, 5734], "\n              Explore our diverse range of property types to find exactly what you&lsquo;re looking for\n            ", [5623, 5734], "\n              Explore our diverse range of property types to find exactly what you&#39;re looking for\n            ", [5623, 5734], "\n              Explore our diverse range of property types to find exactly what you&rsquo;re looking for\n            ", [11263, 11321], "\n              Start Listing Now - It&apos;s FREE!\n            ", [11263, 11321], "\n              Start Listing Now - It&lsquo;s FREE!\n            ", [11263, 11321], "\n              Start Listing Now - It&#39;s FREE!\n            ", [11263, 11321], "\n              Start Listing Now - It&rsquo;s FREE!\n            ", [12754, 12949], "\n                Find your perfect home with our immersive photo experience and comprehensive listings,\n                including exclusive properties you won&apos;t find anywhere else.\n              ", [12754, 12949], "\n                Find your perfect home with our immersive photo experience and comprehensive listings,\n                including exclusive properties you won&lsquo;t find anywhere else.\n              ", [12754, 12949], "\n                Find your perfect home with our immersive photo experience and comprehensive listings,\n                including exclusive properties you won&#39;t find anywhere else.\n              ", [12754, 12949], "\n                Find your perfect home with our immersive photo experience and comprehensive listings,\n                including exclusive properties you won&rsquo;t find anywhere else.\n              ", [2912, 2926], "[fetchPGProperties, searchParams]", [2971, 2974], [2971, 2974], [400, 403], [400, 403], [2748, 2750], [2968, 2977], "[fetchProperties, filters, pagination.limit, pagination.page]", [3204, 3221], "[fetchProperties, pagination.limit, pagination.page]", [4341, 4344], [4341, 4344], [6477, 6480], [6477, 6480], [7195, 7198], [7195, 7198], [7486, 7488], [1931, 1933], [1991, 1994], [1991, 1994], [2109, 2111], [9206, 9401], "\n                    We help you prepare your property for sale, offering advice on staging, minor repairs, and improvements that can increase your property&apos;s value and appeal.\n                  ", [9206, 9401], "\n                    We help you prepare your property for sale, offering advice on staging, minor repairs, and improvements that can increase your property&lsquo;s value and appeal.\n                  ", [9206, 9401], "\n                    We help you prepare your property for sale, offering advice on staging, minor repairs, and improvements that can increase your property&#39;s value and appeal.\n                  ", [9206, 9401], "\n                    We help you prepare your property for sale, offering advice on staging, minor repairs, and improvements that can increase your property&rsquo;s value and appeal.\n                  ", [11882, 12070], "\n                    We coordinate and conduct showings and open houses, highlighting your property&apos;s best features to potential buyers and collecting valuable feedback.\n                  ", [11882, 12070], "\n                    We coordinate and conduct showings and open houses, highlighting your property&lsquo;s best features to potential buyers and collecting valuable feedback.\n                  ", [11882, 12070], "\n                    We coordinate and conduct showings and open houses, highlighting your property&#39;s best features to potential buyers and collecting valuable feedback.\n                  ", [11882, 12070], "\n                    We coordinate and conduct showings and open houses, highlighting your property&rsquo;s best features to potential buyers and collecting valuable feedback.\n                  ", [15188, 15375], "\n                Curious about your property&apos;s value in today's market? Fill out the form to receive a free, no-obligation property valuation from one of our expert agents.\n              ", [15188, 15375], "\n                Curious about your property&lsquo;s value in today's market? Fill out the form to receive a free, no-obligation property valuation from one of our expert agents.\n              ", [15188, 15375], "\n                Curious about your property&#39;s value in today's market? Fill out the form to receive a free, no-obligation property valuation from one of our expert agents.\n              ", [15188, 15375], "\n                Curious about your property&rsquo;s value in today's market? Fill out the form to receive a free, no-obligation property valuation from one of our expert agents.\n              ", [15188, 15375], "\n                Curious about your property's value in today&apos;s market? Fill out the form to receive a free, no-obligation property valuation from one of our expert agents.\n              ", [15188, 15375], "\n                Curious about your property's value in today&lsquo;s market? Fill out the form to receive a free, no-obligation property valuation from one of our expert agents.\n              ", [15188, 15375], "\n                Curious about your property's value in today&#39;s market? Fill out the form to receive a free, no-obligation property valuation from one of our expert agents.\n              ", [15188, 15375], "\n                Curious about your property's value in today&rsquo;s market? Fill out the form to receive a free, no-obligation property valuation from one of our expert agents.\n              ", [15942, 15989], "Understand your property&apos;s current market value", [15942, 15989], "Understand your property&lsquo;s current market value", [15942, 15989], "Understand your property&#39;s current market value", [15942, 15989], "Understand your property&rsquo;s current market value", "&quot;", [27084, 27297], "\n                &quot;We were amazed at how quickly our property sold! The team provided excellent advice on staging our home and their marketing strategy brought in multiple offers above asking price.\"\n              ", "&ldquo;", [27084, 27297], "\n                &ldquo;We were amazed at how quickly our property sold! The team provided excellent advice on staging our home and their marketing strategy brought in multiple offers above asking price.\"\n              ", "&#34;", [27084, 27297], "\n                &#34;We were amazed at how quickly our property sold! The team provided excellent advice on staging our home and their marketing strategy brought in multiple offers above asking price.\"\n              ", "&rdquo;", [27084, 27297], "\n                &rdquo;We were amazed at how quickly our property sold! The team provided excellent advice on staging our home and their marketing strategy brought in multiple offers above asking price.\"\n              ", [27084, 27297], "\n                \"We were amazed at how quickly our property sold! The team provided excellent advice on staging our home and their marketing strategy brought in multiple offers above asking price.&quot;\n              ", [27084, 27297], "\n                \"We were amazed at how quickly our property sold! The team provided excellent advice on staging our home and their marketing strategy brought in multiple offers above asking price.&ldquo;\n              ", [27084, 27297], "\n                \"We were amazed at how quickly our property sold! The team provided excellent advice on staging our home and their marketing strategy brought in multiple offers above asking price.&#34;\n              ", [27084, 27297], "\n                \"We were amazed at how quickly our property sold! The team provided excellent advice on staging our home and their marketing strategy brought in multiple offers above asking price.&rdquo;\n              ", [28859, 29071], "\n                &quot;The selling process was so smooth and stress-free. Our agent was always available to answer questions and guided us through every step. We got more for our home than we expected!\"\n              ", [28859, 29071], "\n                &ldquo;The selling process was so smooth and stress-free. Our agent was always available to answer questions and guided us through every step. We got more for our home than we expected!\"\n              ", [28859, 29071], "\n                &#34;The selling process was so smooth and stress-free. Our agent was always available to answer questions and guided us through every step. We got more for our home than we expected!\"\n              ", [28859, 29071], "\n                &rdquo;The selling process was so smooth and stress-free. Our agent was always available to answer questions and guided us through every step. We got more for our home than we expected!\"\n              ", [28859, 29071], "\n                \"The selling process was so smooth and stress-free. Our agent was always available to answer questions and guided us through every step. We got more for our home than we expected!&quot;\n              ", [28859, 29071], "\n                \"The selling process was so smooth and stress-free. Our agent was always available to answer questions and guided us through every step. We got more for our home than we expected!&ldquo;\n              ", [28859, 29071], "\n                \"The selling process was so smooth and stress-free. Our agent was always available to answer questions and guided us through every step. We got more for our home than we expected!&#34;\n              ", [28859, 29071], "\n                \"The selling process was so smooth and stress-free. Our agent was always available to answer questions and guided us through every step. We got more for our home than we expected!&rdquo;\n              ", [30634, 30850], "\n                &quot;The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn't be happier!\"\n              ", [30634, 30850], "\n                &ldquo;The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn't be happier!\"\n              ", [30634, 30850], "\n                &#34;The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn't be happier!\"\n              ", [30634, 30850], "\n                &rdquo;The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn't be happier!\"\n              ", [30634, 30850], "\n                \"The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn&apos;t be happier!\"\n              ", [30634, 30850], "\n                \"The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn&lsquo;t be happier!\"\n              ", [30634, 30850], "\n                \"The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn&#39;t be happier!\"\n              ", [30634, 30850], "\n                \"The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn&rsquo;t be happier!\"\n              ", [30634, 30850], "\n                \"The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn't be happier!&quot;\n              ", [30634, 30850], "\n                \"The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn't be happier!&ldquo;\n              ", [30634, 30850], "\n                \"The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn't be happier!&#34;\n              ", [30634, 30850], "\n                \"The professional photography and virtual tour made our property stand out online. We had multiple showings in the first week and accepted an offer shortly after. Couldn't be happier!&rdquo;\n              ", [1464, 1467], [1464, 1467], [2023, 2262], "\n              Your trusted partner in finding the perfect property across India. Whether you&apos;re buying, selling, or renting,\n              we provide expert guidance and comprehensive solutions for all your real estate needs.\n            ", [2023, 2262], "\n              Your trusted partner in finding the perfect property across India. Whether you&lsquo;re buying, selling, or renting,\n              we provide expert guidance and comprehensive solutions for all your real estate needs.\n            ", [2023, 2262], "\n              Your trusted partner in finding the perfect property across India. Whether you&#39;re buying, selling, or renting,\n              we provide expert guidance and comprehensive solutions for all your real estate needs.\n            ", [2023, 2262], "\n              Your trusted partner in finding the perfect property across India. Whether you&rsquo;re buying, selling, or renting,\n              we provide expert guidance and comprehensive solutions for all your real estate needs.\n            ", [393, 396], [393, 396], [164, 167], [164, 167], [1303, 1312], "[filters, onFilterChange]", [340, 343], [340, 343], [648, 671], "[fetchAnalytics, properties, timeRange]", [829, 832], [829, 832], [1445, 1448], [1445, 1448], [122, 125], [122, 125], [6418, 6421], [6418, 6421], [7617, 7620], [7617, 7620], [21969, 22153], " Your property listing will be reviewed by our team before it goes live.\n                You will receive a notification once it&apos;s approved or if any changes are needed.\n              ", [21969, 22153], " Your property listing will be reviewed by our team before it goes live.\n                You will receive a notification once it&lsquo;s approved or if any changes are needed.\n              ", [21969, 22153], " Your property listing will be reviewed by our team before it goes live.\n                You will receive a notification once it&#39;s approved or if any changes are needed.\n              ", [21969, 22153], " Your property listing will be reviewed by our team before it goes live.\n                You will receive a notification once it&rsquo;s approved or if any changes are needed.\n              ", [1622, 1636], "\n            &quot;", [1622, 1636], "\n            &ldquo;", [1622, 1636], "\n            &#34;", [1622, 1636], "\n            &rdquo;", [1643, 1655], "&quot;\n          ", [1643, 1655], "&ldquo;\n          ", [1643, 1655], "&#34;\n          ", [1643, 1655], "&rdquo;\n          ", [3475, 3478], [3475, 3478], [3879, 3882], [3879, 3882], [5613, 5616], [5613, 5616], [2028, 2031], [2028, 2031]]