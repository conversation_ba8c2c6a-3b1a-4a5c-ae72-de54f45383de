<?php
// Quick Fix for Admin Login in Production
// Upload and run this script to fix admin login issues

echo "<h1>🔧 Admin Login Fix for Production</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

try {
    // Connect to database
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p class='success'>✅ Database connected successfully</p>";
    
    // Check if admin user exists
    $stmt = $db->prepare('SELECT * FROM User WHERE email = ? AND role = ?');
    $stmt->execute(['<EMAIL>', 'ADMIN']);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        echo "<p class='info'>ℹ️ Admin user already exists. Updating password...</p>";
        
        // Update existing admin password
        $hashedPassword = password_hash('Admin@2024!', PASSWORD_DEFAULT);
        $updateStmt = $db->prepare('UPDATE User SET password = ?, isActive = 1, updatedAt = ? WHERE email = ? AND role = ?');
        $updateStmt->execute([
            $hashedPassword,
            date('Y-m-d H:i:s'),
            '<EMAIL>',
            'ADMIN'
        ]);
        
        echo "<p class='success'>✅ Admin password updated successfully!</p>";
    } else {
        echo "<p class='info'>ℹ️ Creating new admin user...</p>";
        
        // Create new admin user
        $hashedPassword = password_hash('Admin@2024!', PASSWORD_DEFAULT);
        $adminId = 'admin_' . uniqid();
        $now = date('Y-m-d H:i:s');
        
        $insertStmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $insertStmt->execute([
            $adminId,
            'Admin User',
            '<EMAIL>',
            $hashedPassword,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        
        echo "<p class='success'>✅ Admin user created successfully!</p>";
    }
    
    // Verify the admin user
    $verifyStmt = $db->prepare('SELECT id, name, email, role, isActive FROM User WHERE email = ? AND role = ?');
    $verifyStmt->execute(['<EMAIL>', 'ADMIN']);
    $admin = $verifyStmt->fetch();
    
    if ($admin) {
        echo "<h2>✅ Admin User Verified</h2>";
        echo "<p><strong>ID:</strong> " . $admin['id'] . "</p>";
        echo "<p><strong>Name:</strong> " . $admin['name'] . "</p>";
        echo "<p><strong>Email:</strong> " . $admin['email'] . "</p>";
        echo "<p><strong>Role:</strong> " . $admin['role'] . "</p>";
        echo "<p><strong>Active:</strong> " . ($admin['isActive'] ? 'Yes' : 'No') . "</p>";
        
        // Test password verification
        $testStmt = $db->prepare('SELECT password FROM User WHERE email = ? AND role = ?');
        $testStmt->execute(['<EMAIL>', 'ADMIN']);
        $passwordData = $testStmt->fetch();
        
        if ($passwordData && password_verify('Admin@2024!', $passwordData['password'])) {
            echo "<p class='success'>✅ Password verification test passed!</p>";
        } else {
            echo "<p class='error'>❌ Password verification test failed!</p>";
        }
        
        echo "<h2>🔑 Login Credentials</h2>";
        echo "<p><strong>URL:</strong> " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . "/admin/login/</p>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Password:</strong> Admin@2024!</p>";
        
        echo "<h2>🧪 API Test</h2>";
        echo "<p>You can test the login API directly at:</p>";
        echo "<p><strong>API URL:</strong> " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . "/php-backend/api/auth/login.php</p>";
        
        echo "<h2>✅ Fix Complete</h2>";
        echo "<p class='success'>The admin login should now work correctly!</p>";
        echo "<p>If you're still having issues, please run the production-diagnostics.php script for more detailed debugging.</p>";
        
    } else {
        echo "<p class='error'>❌ Failed to verify admin user creation</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check:</p>";
    echo "<ul>";
    echo "<li>Database file exists: prisma/dev.db</li>";
    echo "<li>Database file has proper permissions</li>";
    echo "<li>PHP PDO SQLite extension is installed</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><small>Generated on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
