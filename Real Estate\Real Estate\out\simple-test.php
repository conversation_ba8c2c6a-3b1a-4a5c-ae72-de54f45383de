<?php
// Simple Test Script - Basic diagnostics without complex features

echo "<h1>🔍 Simple System Test</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

echo "<h2>1. PHP Basic Test</h2>";
echo "<p class='success'>✅ PHP is working</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";

echo "<h2>2. File Structure Test</h2>";
$files = [
    'index.html' => 'Homepage',
    'properties/index.html' => 'Properties Page',
    'admin/login/index.html' => 'Admin Login Page',
    'php-backend/api/properties/search.php' => 'Search API',
    'prisma/dev.db' => 'Database File'
];

foreach ($files as $file => $name) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $name: Found</p>";
    } else {
        echo "<p class='error'>❌ $name: Missing</p>";
    }
}

echo "<h2>3. Database Test</h2>";
try {
    if (file_exists('prisma/dev.db')) {
        $db = new PDO('sqlite:prisma/dev.db');
        echo "<p class='success'>✅ Database connection works</p>";
        
        $stmt = $db->query('SELECT COUNT(*) as count FROM Property');
        $count = $stmt->fetch()['count'];
        echo "<p class='success'>✅ Properties in database: $count</p>";
        
        $stmt = $db->query('SELECT COUNT(*) as count FROM User WHERE role = "ADMIN"');
        $adminCount = $stmt->fetch()['count'];
        echo "<p class='success'>✅ Admin users: $adminCount</p>";
        
    } else {
        echo "<p class='error'>❌ Database file not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>4. API Test</h2>";
if (file_exists('php-backend/api/properties/search.php')) {
    echo "<p class='success'>✅ Search API file exists</p>";
    
    // Test if we can include the file
    ob_start();
    $error = false;
    try {
        $_GET['limit'] = 1; // Set a parameter
        include 'php-backend/api/properties/search.php';
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
    $output = ob_get_clean();
    
    if ($error) {
        echo "<p class='error'>❌ API Error: $error</p>";
    } else {
        echo "<p class='success'>✅ API can be executed</p>";
        if (strlen($output) > 0) {
            echo "<p class='info'>API Output Length: " . strlen($output) . " characters</p>";
        }
    }
} else {
    echo "<p class='error'>❌ Search API file missing</p>";
}

echo "<h2>5. Quick Links</h2>";
echo "<p><a href='properties/' target='_blank'>Properties Page</a></p>";
echo "<p><a href='admin/login/' target='_blank'>Admin Login</a></p>";
echo "<p><a href='php-backend/api/properties/search.php?limit=5' target='_blank'>Test API Directly</a></p>";

echo "<h2>6. Next Steps</h2>";
echo "<p>If properties page is empty:</p>";
echo "<ul>";
echo "<li>Check if database has approved properties</li>";
echo "<li>Test API endpoint directly</li>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Generated: " . date('Y-m-d H:i:s') . "</small></p>";
?>
