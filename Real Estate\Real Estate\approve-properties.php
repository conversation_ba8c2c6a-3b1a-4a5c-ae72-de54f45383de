<?php
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "=== Approving Properties ===\n";
    
    // Get all properties
    $stmt = $db->prepare('SELECT id, title, approvalStatus FROM Property');
    $stmt->execute();
    $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($properties) . " properties\n";
    
    // Approve all properties
    $updateStmt = $db->prepare('UPDATE Property SET approvalStatus = "APPROVED", isActive = 1 WHERE id = ?');
    
    foreach ($properties as $property) {
        $updateStmt->execute([$property['id']]);
        echo "Approved: {$property['title']} (ID: {$property['id']})\n";
    }
    
    echo "\n=== Verification ===\n";
    $verifyStmt = $db->prepare('SELECT COUNT(*) as approved FROM Property WHERE approvalStatus = "APPROVED" AND isActive = 1');
    $verifyStmt->execute();
    $result = $verifyStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Total approved and active properties: " . $result['approved'] . "\n";

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
