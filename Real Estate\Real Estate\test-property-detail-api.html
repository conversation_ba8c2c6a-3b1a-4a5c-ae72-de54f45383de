<!DOCTYPE html>
<html>
<head>
    <title>Test Property Detail API</title>
</head>
<body>
    <h1>Test Property Detail API</h1>
    <button onclick="testPropertyDetail()">Test Property Detail</button>
    <div id="result"></div>

    <script>
        async function testPropertyDetail() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing property detail...';

            try {
                const propertyId = 'prop_687b2c06a49d4';
                console.log('Testing property detail for ID:', propertyId);
                
                const response = await fetch(`http://localhost:8000/php-backend/api/properties/get.php?id=${propertyId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                const data = await response.json();
                console.log('Response data:', data);

                resultDiv.innerHTML = `
                    <h3>Property Detail Response:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Property ID:</strong> ${propertyId}</p>
                    <p><strong>Success:</strong> ${data.success}</p>
                    ${data.property ? `
                        <p><strong>Title:</strong> ${data.property.title}</p>
                        <p><strong>Images:</strong> ${JSON.stringify(data.property.images)}</p>
                        <p><strong>Approval Status:</strong> ${data.property.approvalStatus || data.property.approval_status}</p>
                    ` : ''}
                    <details>
                        <summary>Full Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;

            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
