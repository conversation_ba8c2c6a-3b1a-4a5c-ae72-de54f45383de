<?php
// Debug Login - Test all components
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>🔍 Debug Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .success { color: #28a745; } .error { color: #dc3545; } .info { color: #17a2b8; }
        .test-section { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        pre { background: #f1f1f1; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Login System</h1>
        
        <div class="test-section">
            <h2>1. Database Connection Test</h2>
            <?php
            try {
                $db = new PDO('sqlite:prisma/dev.db');
                $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                echo "<p class='success'>✅ Database connection successful</p>";
                
                // Check admin user
                $stmt = $db->prepare('SELECT id, name, email, password, role, isActive FROM User WHERE email = ?');
                $stmt->execute(['<EMAIL>']);
                $admin = $stmt->fetch();
                
                if ($admin) {
                    echo "<p class='success'>✅ Admin user found</p>";
                    echo "<p><strong>Email:</strong> " . htmlspecialchars($admin['email']) . "</p>";
                    echo "<p><strong>Role:</strong> " . htmlspecialchars($admin['role']) . "</p>";
                    echo "<p><strong>Active:</strong> " . ($admin['isActive'] ? 'Yes' : 'No') . "</p>";
                    
                    // Test password
                    if (password_verify('Admin@2024!', $admin['password'])) {
                        echo "<p class='success'>✅ Password verification works</p>";
                    } else {
                        echo "<p class='error'>❌ Password verification failed</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Admin user not found</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>2. Login API Test</h2>
            <?php
            if (file_exists('php-backend/api/auth/login.php')) {
                echo "<p class='success'>✅ Login API file exists</p>";
                
                // Test API with curl
                $loginData = json_encode([
                    'email' => '<EMAIL>',
                    'password' => 'Admin@2024!'
                ]);
                
                echo "<p><strong>Testing API with:</strong></p>";
                echo "<pre>" . htmlspecialchars($loginData) . "</pre>";
                
                // Simulate POST request
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . '/php-backend/api/auth/login.php');
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $loginData);
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
                
                if ($error) {
                    echo "<p class='error'>❌ cURL Error: $error</p>";
                } else if ($response) {
                    echo "<p class='success'>✅ API Response received</p>";
                    echo "<pre>" . htmlspecialchars($response) . "</pre>";
                    
                    $data = json_decode($response, true);
                    if ($data && isset($data['success'])) {
                        if ($data['success']) {
                            echo "<p class='success'>✅ Login API working correctly!</p>";
                        } else {
                            echo "<p class='error'>❌ Login failed: " . ($data['error'] ?? 'Unknown error') . "</p>";
                        }
                    }
                } else {
                    echo "<p class='error'>❌ No response from API</p>";
                }
                
            } else {
                echo "<p class='error'>❌ Login API file not found</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>3. File Structure Check</h2>
            <?php
            $files = [
                'php-backend/config/database.php' => 'Database Config',
                'php-backend/api/auth/login.php' => 'Login API',
                'prisma/dev.db' => 'Database File',
                'admin/login/index.html' => 'Original Admin Login',
                'admin-login-working.html' => 'Working Admin Login'
            ];
            
            foreach ($files as $file => $name) {
                if (file_exists($file)) {
                    echo "<p class='success'>✅ $name: Found</p>";
                } else {
                    echo "<p class='error'>❌ $name: Missing</p>";
                }
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>4. Quick Actions</h2>
            <a href="admin-login-working.html" class="btn">🔐 Try Working Login</a>
            <a href="properties/" class="btn" style="background: #28a745;">🏠 Check Properties</a>
            <a href="test-login-form.php" class="btn" style="background: #17a2b8;">🧪 Test Login Form</a>
            <a href="simple-test.php" class="btn" style="background: #ffc107; color: #000;">📋 Simple Test</a>
        </div>
        
        <div class="test-section">
            <h2>5. Manual Test</h2>
            <p>If the API test above shows success, try the login form again with these exact credentials:</p>
            <div style="background: #e7f3ff; padding: 15px; border-radius: 5px;">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> Admin@2024!</p>
            </div>
        </div>
        
        <hr>
        <p><small>Generated: <?= date('Y-m-d H:i:s') ?></small></p>
    </div>
</body>
</html>
