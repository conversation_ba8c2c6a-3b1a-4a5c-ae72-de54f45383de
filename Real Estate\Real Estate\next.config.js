/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Temporarily disable static export for development
  // output: 'export',
  trailingSlash: true,
  // distDir: 'out',
  images: {
    domains: ['images.unsplash.com', 'plus.unsplash.com'],
    unoptimized: true,
  },
  // Add rewrites for API proxy in development
  async rewrites() {
    if (process.env.NODE_ENV === 'development') {
      return [
        {
          source: '/php-backend/api/:path*',
          destination: 'http://localhost:8000/php-backend/api/:path*',
        },
      ];
    }
    return [];
  },
  // Environment variables for API endpoints
  env: {
    NEXT_PUBLIC_API_URL: process.env.NODE_ENV === 'production'
      ? '/php-backend/api'
      : '/php-backend/api', // Use relative URL in development with proxy
  },
};

module.exports = nextConfig;