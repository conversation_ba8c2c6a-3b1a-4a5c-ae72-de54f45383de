/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Only use export for production builds
  ...(process.env.NODE_ENV === 'production' && {
    output: 'export',
    trailingSlash: true,
    distDir: 'out',
  }),
  // Disable ESLint during builds to avoid blocking
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable TypeScript checking during builds
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    domains: ['images.unsplash.com', 'plus.unsplash.com'],
    unoptimized: true,
  },
  // Add rewrites for API proxy in development only
  async rewrites() {
    if (process.env.NODE_ENV === 'development') {
      return [
        {
          source: '/php-backend/api/:path*',
          destination: 'http://localhost:8000/php-backend/api/:path*',
        },
      ];
    }
    return [];
  },
  // Environment variables for API endpoints
  env: {
    NEXT_PUBLIC_API_URL: process.env.NODE_ENV === 'production'
      ? '/php-backend/api'
      : '/php-backend/api',
  },
};

module.exports = nextConfig;